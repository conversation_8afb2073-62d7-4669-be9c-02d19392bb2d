import { Tab, Tabs } from "@heroui/tabs";
import { Avatar } from "@heroui/avatar";
import {
  ArrowDown01Icon,
  ArrowLeft01Icon,
  ArrowUpRight01Icon,
  Delete02Icon,
  Download01Icon,
  Download04Icon,
  PencilEdit01Icon,
  Upload04Icon,
  ViewIcon,
  ViewOffSlashIcon,
} from "hugeicons-react";
import { Button } from "@heroui/button";
import { SignupSteps } from "@/core/constants/signup.js";
import startsWith from "lodash.startswith";
import PhoneInput from "react-phone-input-2";
import { useEffect, useRef, useState } from "react";
import { registerStep4Schema } from "@shared/schemes/userSchema.js";
import UnderlinedInput from "@/modules/settings/components/UnderlinedInput.jsx";
import { COUNTRIES } from "@/core/constants/countries.js";
import CountrySelector from "@shared/components/CountrySelector.jsx";
import { Select, SelectItem } from "@heroui/select";
import { useNavigate } from "react-router-dom";
import { Switch } from "@heroui/switch";
import Bank from "@shared/assets/images/bank.svg";
import blueTrophy from "@shared/assets/images/settings/trophy.svg";
import masterCard from "@shared/assets/images/settings/masterCard.svg";
import Wise from "@shared/assets/images/wise.svg";
import Payoneer from "@shared/assets/images/payoneer.svg";
import AddMethodModal from "@/modules/settings/components/AddMethodModal.jsx";
import AddWiseModal from "@/modules/settings/components/AddWiseModal.jsx";
import AddPayoneerModal from "@/modules/settings/components/AddPayoneerModal";
import { QRCodeCanvas, QRCodeSVG } from 'qrcode.react';
import DashboardLayout from "../../shared/layouts/DashboardLayout";
import NumTelSelect from "../../shared/components/NumTelSelect";
import { toSlug } from "../../../core/utils/functions";
import { motion } from "framer-motion";
import { div } from "framer-motion/client";
import CustomTable from "../../shared/components/CustomTable";
import UpgradePlanModal from "../components/UpgradePlanModal";
import UpdateCreditCardModal from "../components/UpdateCreditCardModal";
import { getUserInfos } from "../../../core/services/UserHandler";
import { useDispatch, useSelector } from "react-redux";
import { get2FAStatus, UpdatePassword, UpdateProfile } from "../../../core/redux/slices/profile/profileSlice";
import { Input } from "@heroui/input";
import Toggle2FAModal from "../components/Toggle2FAModal";
import { Spinner } from "@heroui/react";

const notificationItems = [
  {
    title: "General Notifications",
    subtitle:
      "Get notified about new offers, promotions, and other important information.",
  },
  { title: "Platform Updates", subtitle: "Get notified about new updates and features." },
  { title: "Sound", subtitle: "Receive notification with a sound effect" },
  {
    title: "Email Notifications",
    subtitle: "Get notified about new offers, promotions, and other important information.",
  },
  { title: "Mute", subtitle: "Turn off notifications" },
];

const NAV_OPT = [
  "Account Settings",
  "Login & Security",
  // "Notifications",
  // "My Subscription",
  // "Withdrawal Methods",
];

/* ------------------
   NAVIGATOR
------------------ */
const Navigator = ({ navItems, currentTab, setCurrentTab }) => {
  const containerRef = useRef(); // Ref for the parent container
  const itemRefs = useRef([]); // Ref array for each nav item

  const handleTabClick = (slug, idx) => {
    setCurrentTab(slug);

    // Scroll the clicked tab into view
    const element = itemRefs.current[idx];
    if (element) {
      element.scrollIntoView({
        behavior: "smooth", // Smooth scrolling
        block: "center",
        inline: "center",
      });
    }
  };

  return (
    <div ref={containerRef} className="w-full  hide-scrollbar flex justify-start items-start gap-4">
      {navItems.map((i, idx) => {
        const slug = toSlug(i);
        return (
          <div
            ref={(el) => (itemRefs.current[idx] = el)} // Store the ref for each item
            onClick={() => handleTabClick(slug, idx)}
            key={idx}
            className="relative p-3 text-nowrap cursor-pointer hover:opacity-50 text-black dark:text-white"
          >
            <span
              className={`${currentTab === slug ? "opacity-100" : "opacity-30"
                } text-sm md:text-base lg:text-lg`}
            >
              {i}
            </span>
            {currentTab === slug && (
              <motion.div
                layoutId="underline" // Unique ID for shared layout animations
                className="absolute bottom-0 left-0 right-0 h-1 bg-glb_blue"
                style={{ borderRadius: "4px" }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

/* ------------------
   ACCOUNT SETTINGS
------------------ */
const AccountSettings = () => {
  const [openTelCode, setOpenTelCode] = useState(false);
  const dispatch = useDispatch();
  const { loading, error } = useSelector((state) => state.profile);

  const [isCountrySelectOpen, setIsCountrySelectOpen] = useState(false);
  const [country, setCountry] = useState(SignupSteps.defaultCountry);
  const [telephone, setTelephone] = useState({
    code: "🇲🇦 +212",
    num: "",
  });

  let user = getUserInfos()


  const [formData, setFormData] = useState({
    fullName: '',
    legalName: '',
    cardId: '',
    email: '',
    phone: '',
    address: '',
    taxId: '',
    commercialReg: '',
  });

  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullname || '',
        legalName: user.legalName || '',
        cardId: user.cardID || '',
        email: user.email || '',
        phone: user.phoneNumber || '',
        address: user.address || '',
        taxId: user.taxID || '',
        commercialReg: user.commercialRegister || '',
        logo: user.logo || '',
      });
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = (e) => {
    dispatch(UpdateProfile({ profile: formData }));

    // Add your form submission logic here
  };

  const resetProfile = () => {
    user = getUserInfos()
    if (user) {
      setFormData({
        fullName: user.fullname || '',
        legalName: user.legalName || '',
        cardId: user.cardID || '',
        email: user.email || '',
        phone: user.phoneNumber || '',
        address: user.address || '',
        taxId: user.taxID || '',
        commercialReg: user.commercialRegister || '',
        logo: user.logo || '',
      });
    }

  }


  const updateTelephone = (property, newValue) => {
    if (property === "num") {
      if (/^\d*$/.test(newValue) === true) {
        setTelephone((prevState) => ({
          ...prevState,
          [property]: newValue,
        }));
      }
    } else {
      setTelephone((prevState) => ({
        ...prevState,
        [property]: newValue,
      }));
    }
  };
  const [imageSrc, setImageSrc] = useState(user.logo);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFormData((prevData) => ({
        ...prevData,
        logo: file,
      }));
      const reader = new FileReader();
      reader.onload = () => setImageSrc(reader.result);
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="mt-10 md:px-10 xl:px-14 mx-auto flex items-start gap-6 flex-col lg:flex-row max-w-[1000px]">
      <div className="group relative rounded-lg w-32 h-32 border border-[#00000030] dark:border-[#ffffff30] overflow-hidden mx-auto">
        {!imageSrc ? (
          <label className="cursor-pointer flex items-center justify-center w-full h-full bg-transparent rounded-lg">
            <Upload04Icon size={24} className="text-gray-500" />
            <input type="file" accept="image/*" onChange={handleImageUpload} className="hidden" />
          </label>
        ) : (
          <>
            <div className="hidden group-hover:flex  absolute inset-0  items-center z-10 gap-2 flex-col justify-center bg-zinc-900/60">
              <Button onClick={() => { setImageSrc(null); setFormData((prevData) => ({ ...prevData, logo: null })); }} isIconOnly className="border rounded-full" variant="light">
                <Delete02Icon size={18} className="text-white" />
              </Button>
              <span className="text-sm text-white">Delete</span>
            </div>
            <Avatar className="w-32 h-32 rounded-lg" src={imageSrc} alt="Profile Picture" />
          </>
        )}
      </div>
      <div className="flex-1 space-y-4 mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UnderlinedInput start={true} value={formData.fullName} onChange={(e) => handleChange(e)} label="Full Name" name="fullName" />
          <UnderlinedInput start={true} value={formData.legalName} onChange={(e) => handleChange(e)} label="Legal Name" name="legalName" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UnderlinedInput start={true} value={formData.cardId} onChange={(e) => handleChange(e)} label="Card ID" name="cardId" />
          <UnderlinedInput isDisabled start={true} value={formData.email} onChange={(e) => handleChange(e)} label="Email" name="email" />
        </div>


        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
          <div className="flex gap-2 md:mt-12">
            <UnderlinedInput start={true} value={formData.phone} onChange={(e) => handleChange(e)} label="Phone" name="phone" />
            {/* <NumTelSelect
              id={"countries_code"}
              open={openTelCode}
              onToggle={() => setOpenTelCode(!openTelCode)}
              onChange={updateTelephone}
              selectedValue={telephone}
            /> */}
          </div>
          <UnderlinedInput start={true} value={formData.address} onChange={(e) => handleChange(e)} label="Address" name="address" className={"md:mt-12"} />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UnderlinedInput start={true} label="Tax ID" name="taxId" value={formData.taxId} onChange={(e) => handleChange(e)} />
          <UnderlinedInput start={true} label="Commercial Registration" value={formData.commercialReg} onChange={(e) => handleChange(e)} name="commercialReg" />
        </div>


        {/* Error Message */}
        <div className="flex flex-col gap-4 mt-8">


          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button isLoading={loading} onClick={() => handleSubmit()} color="primary" className="bg-glb_blue rounded-full">
              Update Profile
            </Button>
            <Button onClick={() => resetProfile()} color="danger" variant="light" className="rounded-full">
              Reset All
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

/* ------------------
   LOGIN & SECURITY
------------------ */
const LoginSecurity = ({ twoFAData, setTwoFAData, loading2FA }) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfNewPassword, setShowConfNewPassword] = useState(false);
  const [validation, setValidation] = useState({});
  const dispatch = useDispatch();

  const { loading } = useSelector((state) => state.profile);
  const [is2FAModalOpen, setIs2FAModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    oldPassword: "",
    newPassword: "",
    newPassword_confirmation: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = () => {
    dispatch(UpdatePassword({ data: formData }));
  };



  return (
    <>
      <div className="mt-10 md:px-10 xl:px-20 max-w-6xl mx-auto">
        <h3 className="">Update Password</h3>
        <div className="grid grid-cols-1 my-5 md:grid-cols-3 gap-4">

          <Input
            type={showPassword ? 'text' : 'password'} className="my-3" placeholder="Current password"
            value={formData.oldPassword}
            variant="underlined"
            color="primary"
            classNames={{
              label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
            }}
            endContent={showPassword ?
              <ViewOffSlashIcon className="cursor-pointer" onClick={() => setShowPassword(false)} /> :
              <ViewIcon className="cursor-pointer" onClick={() => setShowPassword(true)} />
            }
            autoComplete="false"
            onChange={handleChange} name={'oldPassword'} required />

          <Input type={showNewPassword ? 'text' : 'password'} className="my-3" placeholder="New password"
            value={formData.newPassword}
            variant="underlined"
            color="primary"
            autoComplete="false"
            classNames={{
              label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
            }}
            endContent={showNewPassword ?
              <ViewOffSlashIcon className="cursor-pointer" onClick={() => setShowNewPassword(false)} /> :
              <ViewIcon className="cursor-pointer" onClick={() => setShowNewPassword(true)} />
            }
            onChange={handleChange} name={'newPassword'} required />

          <Input type={showConfNewPassword ? 'text' : 'password'} className="my-3" placeholder="Confirm new password"
            value={formData.newPassword_confirmation}
            variant="underlined"
            color="primary"
            autoComplete="false"
            classNames={{
              label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
            }}
            endContent={showConfNewPassword ?
              <ViewOffSlashIcon className="cursor-pointer" onClick={() => setShowConfNewPassword(false)} /> :
              <ViewIcon className="cursor-pointer" onClick={() => setShowConfNewPassword(true)} />
            }
            onChange={handleChange} name={'newPassword_confirmation'} required />

        </div>
        <Button isLoading={loading} onClick={() => handleSubmit()} color="primary" className="bg-glb_blue rounded-full mt-2 float-right">
          Update Password
        </Button>
      </div>
      <div className="h-[1px] bg-black/10 dark:bg-white/10 my-24"></div>
      <label
        htmlFor="two-factor-authentication"
        className="flex flex-row justify-between items-center gap-4 md:px-12 max-w-[800px]"
      >
        <div>
          <h4 className="font-medium">Two-Factor Authentication (2FA)</h4>
          <p className="text-sm text-gray-500">Increase security for your account by using multiple authentication steps.</p>
        </div>

        {loading2FA ? <Spinner /> : (
          <div
            onClick={() => setIs2FAModalOpen(true)}
            className="relative w-14 h-8 cursor-pointer"
          >
            <div className={`
              absolute w-full h-full rounded-full transition-colors duration-300
              ${twoFAData?.enabled ? 'bg-glb_blue' : 'bg-gray-300'}
            `} />
            <div className={`
              absolute w-6 h-6 bg-white rounded-full shadow-md transform transition-transform duration-300 top-1
              ${twoFAData?.enabled ? 'translate-x-7' : 'translate-x-1'}
            `} />
          </div>
        )}
      </label>
      {twoFAData?.enabled && twoFAData?.qrCode && (<div className="mt-4 rounded md:px-12 max-w-[800px] flex flex-row flex-wrap justify-start items-start gap-4">
        <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
          <p className="text-yellow-800 dark:text-yellow-200 text-sm font-medium">
            ⚠️ Important: Scan this QR code before logging out
          </p>
          <p className="text-yellow-700 dark:text-yellow-300 text-sm mt-1">
            You'll need the verification code from Google Authenticator to log back in after enabling 2FA.
          </p>
        </div>
        <div className="flex justify-center">
          {twoFAData?.qrCode ? (
            <QRCodeCanvas value={twoFAData.qrCode} />) : (
            <div className="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center">                                    QR Code will appear here
            </div>
          )}
        </div>
        <div className="flex flex-col gap-4 text-sm text-gray-500">
          <h4 className="font-bold text-base">Instructions</h4>
          <ul className="list-decimal list-inside space-y-2">
            <li className="text-sm">Install Google Authenticator on your phone</li>
            <li className="text-sm">Scan the QR code with the app</li>
            <li className="text-sm">Enter the verification code shown in the app</li>
          </ul>
        </div>
      </div>)}
      <Toggle2FAModal
        isOpen={is2FAModalOpen}
        onClose={() => setIs2FAModalOpen(false)}
        twoFAData={twoFAData}
        setTwoFAData={setTwoFAData}
      />
    </>
  );
};

/* ------------------
   NOTIFICATIONS
------------------ */
const Notifications = () => {
  return (
    <div className="mt-10  md:px-10 xl:px-20 mx-auto">
      <div className="flex flex-col justify-between items-center gap-4 px-2 md:px-12 w-full py-6">
        {notificationItems.map((item, index) => (
          <label key={index} className="flex flex-row gap-2 w-full justify-between items-center">
            <div className="flex flex-col justify-start items-stretch">
              <h4 className="font-medium text-start">{item.title}</h4>
              <p className="text-sm text-gray-500">{item.subtitle}</p>
            </div>
            <Switch id={item.title} />
          </label>
        ))}
      </div>
    </div>
  );
};

/* ------------------
   MY SUBSCRIPTION
------------------ */
const MySubscription = ({ setOpenModal, setOpenCCModal }) => {
  const [isCancelPopupOpen, setIsCancelPopupOpen] = useState(false);
  const openCancelPopup = () => setIsCancelPopupOpen(true);
  const closeCancelPopup = () => setIsCancelPopupOpen(false);

  const tableData = [
    {
      key: "1",
      invoiceId: "INV_19287",
      billingDate: "23 Sep 2024",
      planSelected: "Basic",
      price: "1,981 USD",
      status: "Paid",
    },
    {
      key: "2",
      invoiceId: "INV_19276",
      billingDate: "22 Sep 2023",
      planSelected: "Basic",
      price: "1,781 USD",
      status: "Paid",
    },
  ];

  const columns = [
    { key: "invoiceId", label: "Invoice ID", sortable: true },
    { key: "billingDate", label: "Billing Date", sortable: true },
    { key: "planSelected", label: "Plan Selected", sortable: true },
    { key: "price", label: "Price", sortable: true },
    { key: "status", label: "Status" },
    { key: "actions", label: "Actions", w: "w-[10%]" },
  ];

  const renderCell = (item, columnKey) => {
    switch (columnKey) {
      case "actions":
        return (
          <div className="flex flex-row gap-2 justify-center">
            <Button variant="solid" className={`rounded-full text-white bg-glb_blue`}>
              <Download04Icon size={18} />
              Download
            </Button>
          </div>
        );
      default:
        return <span className="text-sm dark:text-white">{item[columnKey]}</span>;
    }
  };

  return (
    <div className="mt-10 md:px-10 xl:px-20 mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 w-full">
        <div className="flex flex-col  col-span-1">
          <h3 className="text-base font-medium">Current Plan</h3>
          <div className="flex flex-col gap-8 mt-3 p-6 flex-grow rounded-3xl bg-[#00000005] dark:bg-[#ffffff05] border-1 border-[#00000020] dark:border-[#ECECEC05]">
            <div className="flex justify-between items-center gap-2 w-full">
              <div className="flex justify-start items-center gap-2">
                <img src={blueTrophy} alt="Current plan" className="w-10 lg:w-12 " />
                <div className="flex flex-col gap-1">
                  <span className=" text-sm lg:text-base">Basic</span>
                  <span className="text-xs lg:text-sm text-[#00000050] dark:text-[#ffffff50]">
                    Monthly Subsription $39
                  </span>
                </div>
              </div>
              <div className="flex flex-col justify-end gap-1">
                <span className="text-xs lg:text-sm text-[#00000050] dark:text-[#ffffff50]">Next Billing Date</span>
                <span className="text-sm lg:text-base">04/05/2024</span>
              </div>
            </div>
            <div className="flex justify-between items-center gap-2 w-full">
              <Button
                variant="solid"
                onClick={() => setOpenModal(true)}
                className="bg-glb_blue  px-2 lg:py-2 lg:px-4 group rounded-full text-sm  text-white"
              >
                Upgrade Now
                <ArrowUpRight01Icon
                  size={18}
                  className="transform transition-transform duration-500 group-hover:translate-x-1 group-hover:-translate-y-1"
                />
              </Button>
              <span onClick={openCancelPopup} className="cursor-pointer ... text-glb_red text-end">
                Cancel Your Plan
              </span>
            </div>
          </div>
          {isCancelPopupOpen && (
            <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
              {/* Dark-mode-friendly backdrop */}
              <div className="absolute inset-0 bg-black/50" onClick={closeCancelPopup} />
              {/* Popup card, with dark mode classes */}
              <div
                className="
                relative
                bg-white dark:bg-[#1f1f1f]
                text-black dark:text-gray-100
                p-6 rounded-xl shadow-lg
                max-w-sm w-full text-center
                font-bold
              "
              >
                <p>Are you sure you want to cancel your current plan?</p>
                <div className="mt-4 flex gap-4 justify-center ">
                  <Button onClick={closeCancelPopup} className="rounded-full">
                    Back
                  </Button>
                  <Button
                    color="danger"
                    className="rounded-full"
                    onClick={() => {
                      // handle your cancel logic here
                      closeCancelPopup();
                    }}
                  >
                    Yes
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col  col-span-1 ">
          <h3 className="text-base font-medium">Payment Method</h3>
          <div className="flex justify-between items-start mt-3 p-6 min-h-[175px] flex-grow rounded-3xl bg-[#00000005] dark:bg-[#ffffff05] border-1 border-[#00000020] dark:border-[#ECECEC05]">
            <div className="flex justify-center items-start gap-2">
              <img src={masterCard} alt="Master Card" className="w-12 lg:w-20" />
              <div className="flex flex-col gap-1">
                <span className="text-sm lg:text-lg font-medium">Master Card</span>
                <span className="text-sm lg:text-lg font-medium">•••• •••• •••• ••02</span>
                <span className="text-sm lg:text-base font-medium text-[#00000050] dark:text-[#ffffff50]">
                  Expiry on 20/2024
                </span>
              </div>
            </div>
            <Button isIconOnly onClick={() => setOpenCCModal(true)} className="bg-transparent">
              <PencilEdit01Icon />
            </Button>
          </div>
        </div>
      </div>
      <div className="w-full mt-10">
        <h3 className="text-base font-medium">Subscription Invoices</h3>
        <CustomTable
          data={tableData}
          columns={columns}
          renderCell={renderCell} // Pass selected rows state
          className="dark:bg-gray-800 dark:text-white" // Dark mode support
        />
      </div>
    </div>
  );
};

/* ------------------
   WITHDRAWAL METHODS
------------------ */
const WithdrawalMethods = ({
  setIsAddMethodModalOpen,
  setIsWiseMethodModalOpen,
  setIsPayoneerMethodModalOpen
}) => {
  return (
    <div className="flex flex-row items-center justify-center gap-4 
                flex-wrap lg:flex-nowrap mt-5 lg:mt-24">        {/* 1) Bank Transfer */}
      <div className="m-4 max-w-[400px] bg-white dark:bg-zinc-950 border border-gray-200 
          dark:border-zinc-900 hover:bg-gray-200/20 dark:hover:bg-zinc-900/40 
          transition-colors inline-block w-full md:w-1/2 lg:w-1/3 rounded-lg"
      >
        <div className="p-8">
          <div className="flex flex-col justify-center gap-6 h-full">
            <div className="mb-4 flex flex-col justify-center items-center gap-6">
              <img src={Bank} alt="Bank Transfer" className="w-20 h-20" />
              <h2 className="text-lg font-bold text-center">Bank Transfer</h2>
              <p className="text-gray-500 text-sm mt-1 text-center">
                Provide details about shipping services, so you can make informed
                decision about costs
              </p>
            </div>

            <Button
              className="rounded-full font-medium w-fit mx-auto px-6 bg-dark/10 bg-info text-white 
                  transition-colors ease-in-out text-sm flex flex-row py-5 items-center justify-center 
                  gap-2 shadow"
              onClick={() => setIsAddMethodModalOpen(true)}
            >
              Setup This Method
            </Button>
          </div>
        </div>
      </div>

      {/* 2) Wise */}
      <div className="m-4 max-w-[400px] bg-white dark:bg-zinc-950 border border-gray-200 
            dark:border-zinc-900 hover:bg-gray-200/20 dark:hover:bg-zinc-900/40 
            transition-colors inline-block w-full md:w-1/2 lg:w-1/3 rounded-lg"
      >
        <div className="p-8">
          <div className="flex flex-col justify-center gap-6 h-full">
            <div className="mb-4 flex flex-col justify-center items-center gap-6">
              <img src={Wise} alt="Wise" className="w-20 h-20" />
              <h2 className="text-lg font-bold text-center">Wise Account</h2>
              <p className="text-gray-500 text-sm mt-1 text-center">
                Provide details about shipping services, so you can make informed
                decision about costs
              </p>
            </div>

            <Button
              className="bg-info rounded-full font-medium w-fit px-6 
                  hover:bg-info text-white transition-colors ease-in-out 
                  text-sm flex flex-row py-5 items-center justify-center 
                  gap-2 shadow mx-auto"
              onClick={() => setIsWiseMethodModalOpen(true)} // <-- calls Wise modal
            >
              Setup This Method
            </Button>
          </div>
        </div>
      </div>

      {/* 3) Payoneer */}
      <div className="m-4 max-w-[400px] bg-white dark:bg-zinc-950 border border-gray-200 
            dark:border-zinc-900 hover:bg-gray-200/20 dark:hover:bg-zinc-900/40 
            transition-colors inline-block w-full md:w-1/2 lg:w-1/3 rounded-lg"
      >
        <div className="p-8">
          <div className="flex flex-col justify-center gap-6 h-full">
            <div className="mb-4 flex flex-col justify-center items-center gap-6">
              <div className="flex flex-row items-center gap-2 h-20">
                <img src={Payoneer} alt="Payoneer" className="w-8 h-8" />
                <h5 className="font-medium text-3xl">Payoneer</h5>
              </div>
              <h2 className="text-lg font-bold text-center">Payoneer Account</h2>
              <p className="text-gray-500 text-sm mt-1 text-center">
                Provide details about shipping services, so you can make informed
                decision about costs
              </p>
            </div>

            <Button
              className="bg-info rounded-full font-medium w-fit px-6 
                  hover:bg-info text-white transition-colors ease-in-out 
                  text-sm flex flex-row py-5 items-center justify-center 
                  gap-2 shadow mx-auto"
              onClick={() => setIsPayoneerMethodModalOpen(true)}
            >
              Setup This Method
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

/* ------------------
   SETTINGS (Main)
------------------ */
const Settings = () => {
  const [isAddMethodModalOpen, setIsAddMethodModalOpen] = useState(false);
  const [isUpgradePlanModalOpen, setIsUpgradePlanModalOpen] = useState(false);
  const [isUpdateCCModalOpen, setIsUpdateCCModalOpen] = useState(false);
  const [isWiseMethodModalOpen, setIsWiseMethodModalOpen] = useState(false);
  const [isPayoneerMethodModalOpen, setIsPayoneerMethodModalOpen] = useState(false);

  const [currentTab, setCurrentTab] = useState("account-settings");

  // Local 2FA state management
  const [twoFAData, setTwoFAData] = useState({
    enabled: null,
    secret: null,
    qrCode: null,
  });
  const [loading2FA, setLoading2FA] = useState(false);
  const dispatch = useDispatch();

  // Fetch 2FA status when component mounts
  useEffect(() => {
    const fetch2FAStatus = async () => {
      setLoading2FA(true);
      try {
        const result = await dispatch(get2FAStatus());
        if (get2FAStatus.fulfilled.match(result)) {
          setTwoFAData({
            enabled: result.payload.enabled,
            secret: result.payload.secret,
            qrCode: result.payload.qrCode,
          });
        }
      } catch (error) {
        console.error('Failed to fetch 2FA status:', error);
      } finally {
        setLoading2FA(false);
      }
    };

    fetch2FAStatus();
  }, [dispatch]);

  // Reset 2FA state when component unmounts (user quits settings)
  useEffect(() => {
    return () => {
      setTwoFAData({
        enabled: null,
        secret: null,
        qrCode: null,
      });
    };
  }, []);

  const renderComponent = () => {
    switch (currentTab) {
      case "account-settings":
        return <AccountSettings />;
      case "login-and-security":
        return <LoginSecurity
          twoFAData={twoFAData}
          setTwoFAData={setTwoFAData}
          loading2FA={loading2FA}
        />;
      case "notifications":
        return <Notifications />;
      case "my-subscription":
        return <MySubscription setOpenModal={setIsUpgradePlanModalOpen} setOpenCCModal={setIsUpdateCCModalOpen} />;
      case "withdrawal-methods":
        /* PASS setIsAddMethodModalOpen HERE */
        return <WithdrawalMethods
          setIsAddMethodModalOpen={setIsAddMethodModalOpen}
          setIsWiseMethodModalOpen={setIsWiseMethodModalOpen}
          setIsPayoneerMethodModalOpen={setIsPayoneerMethodModalOpen}

        />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <>
      <DashboardLayout hasReturnLink={true} title="Settings" bgColor="bg-greyish">
        <Navigator navItems={NAV_OPT} currentTab={currentTab} setCurrentTab={setCurrentTab} />
        <div className="w-full">{renderComponent()}</div>
      </DashboardLayout>

      {/* The modals */}
      <AddMethodModal isOpen={isAddMethodModalOpen} onClose={() => setIsAddMethodModalOpen(false)} />
      <AddWiseModal
        isOpen={isWiseMethodModalOpen}
        onClose={() => setIsWiseMethodModalOpen(false)}
      />
      <AddPayoneerModal isOpen={isPayoneerMethodModalOpen} onClose={() => setIsPayoneerMethodModalOpen(false)} />

      <UpgradePlanModal isOpen={isUpgradePlanModalOpen} onClose={() => setIsUpgradePlanModalOpen(false)} />
      <UpdateCreditCardModal isOpen={isUpdateCCModalOpen} onClose={() => setIsUpdateCCModalOpen(false)} />

    </>
  );
};

export default Settings;
