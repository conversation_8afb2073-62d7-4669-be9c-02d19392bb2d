import { toggle2faUrl, twofaStatusUrl, updatePasswordUrl, updateProfileUrl } from "../URLs";
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { toast } from 'sonner';
import { getToken } from "../../../services/TokenHandler";

// Initial state
const initialState = {
    loading: false,
    error: null,
};



// Async thunk for updating profiles
export const UpdateProfile = createAsyncThunk(
    'profiles/Updateprofile',
    async ({ profile }, { rejectWithValue }) => {
        try {
            const formData = new FormData();
            formData.append('_method', 'PUT');
            formData.append('fullname', profile.fullName || '');
            formData.append('legalName', profile.legalName || '');
            formData.append('cardID', profile.cardId || '');
            formData.append('email', profile.email || '');
            formData.append('phoneNumber', profile.phone || '');
            formData.append('address', profile.address || '');
            formData.append('taxID', profile.taxId || '');
            formData.append('commercialRegister', profile.commercialReg || '');
            formData.append('logo', profile.logo);


            const response = await axios.post(`${updateProfileUrl}`, formData, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error updating profiles' });
            } else {
                toast.success("Profil updated successfuly")
            }

            if (response.data.result) {
                localStorage.setItem('xM_htUju', JSON.stringify(response.data.result));
                // Dispatch a custom event to notify components about auth change
                window.dispatchEvent(new CustomEvent('auth-change'));
            }




        } catch (error) {
            console.log(error.response?.status, error.response?.data.message);
            if (error.response?.data.message) {
                error.response?.data.message.split(',').map(m => toast.error(m))
            }
            return rejectWithValue({ status: error.response?.status, message: error.response?.data.message || 'Something went wrong' });
        }
    }
);


// Async thunk for updating profiles
export const getProfile = createAsyncThunk(
    'profiles/getProfile',
    async (_, { rejectWithValue }) => {
        try {


            const response = await axios.get(`${updateProfileUrl}`, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error updating profiles' });
            }

            if (response.data.result) {
                localStorage.setItem('xM_htUju', JSON.stringify(response.data.result));
                // Dispatch a custom event to notify components about auth change
                window.dispatchEvent(new CustomEvent('auth-change'));
            }


        } catch (error) {
            console.log(error.response?.status, error.response?.data.message);
            if (error.response?.data.message) {
                error.response?.data.message.split(',').map(m => console.log(m))
            }
            return rejectWithValue({ status: error.response?.status, message: error.response?.data.message || 'Something went wrong' });
        }
    }
);

export const get2FAStatus = createAsyncThunk(
    'profiles/get2FAStatus',
    async (_, { rejectWithValue }) => {
        try {

            const response = await axios.get(`${twofaStatusUrl}`, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching 2FA status' });
            }

            return response.data.result;


        } catch (error) {
            console.log(error.response?.status, error.response?.data.message);

            return rejectWithValue({ status: error.response?.status, message: error.response?.data.message || 'Something went wrong' });
        }
    }
);

export const toggle2FA = createAsyncThunk(
    'profiles/toggle2FA',
    async ({ enable, password }, { rejectWithValue }) => {
        try {
            const response = await axios.post(`${toggle2faUrl}`, { enable, password }, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error fetching 2FA status' });
            }

            toast.success(response.data.message)
            return response.data.result;


        } catch (error) {
            console.log(error.response?.status, error.response?.data.message);

            return rejectWithValue({ status: error.response?.status, message: error.response?.data.message || 'Something went wrong' });
        }
    }
);

// Async thunk for updating profiles
export const UpdatePassword = createAsyncThunk(
    'profiles/Updatepassword',
    async ({ data }, { rejectWithValue }) => {
        try {

            const response = await axios.put(`${updatePasswordUrl}`, {
                oldPassword: data.oldPassword,
                newPassword: data.newPassword,
                newPassword_confirmation: data.newPassword_confirmation,
            }, {
                headers: {
                    Authorization: `Bearer ${getToken()}`,
                },
            });

            if (response.data.response !== 'success') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error updating profiles' });
            } else {
                toast.success("Paasword updated successfuly")
            }

        } catch (error) {
            console.log(error.response?.status, error.response?.data.message);
            if (error.response?.data.message) {
                error.response?.data.message.split(',').map(m => console.log(m))
            }
            return rejectWithValue({ status: error.response?.status, message: error.response?.data.message || 'Something went wrong' });
        }
    }
);



const profileSlice = createSlice({
    name: 'profile',
    initialState,
    reducers: {
        // Add any synchronous reducers if needed
    },
    extraReducers: (builder) => {
        builder
            .addCase(UpdateProfile.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(UpdateProfile.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(UpdateProfile.rejected, (state, action) => {
                state.loading = false;

            })
            .addCase(UpdatePassword.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(UpdatePassword.fulfilled, (state, action) => {
                state.loading = false;
            })
            .addCase(UpdatePassword.rejected, (state, action) => {
                state.loading = false;

            })

    },
});

export default profileSlice.reducer;


