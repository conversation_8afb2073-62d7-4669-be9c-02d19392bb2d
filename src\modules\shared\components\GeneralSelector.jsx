import PropTypes from 'prop-types';
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { ArrowDown01Icon, Cancel01Icon } from "hugeicons-react";

export default function GeneralSelector({
    id,
    open,
    disabled = false,
    onToggle,
    onChange,
    selectedValue,
    options,
    loading = false,
    placeholder = "Select...",
    onSearchChange,
    onEndScroll,
    useAll = true,
    excludeAll = false, // Add excludeAll prop
    selectMultiple = false, // Add selectMultiple prop
    dropdownDirection = 'auto', // 'auto', 'up', 'down'
}) {
    const ref = useRef(null);
    const scrollRef = useRef(null);
    const [query, setQuery] = useState("");
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [dropdownPosition, setDropdownPosition] = useState('bottom');

    // Helper function to get selected IDs from selected objects
    const getSelectedIds = (selectedItems) => {
        if (!selectedItems) return [];
        if (!Array.isArray(selectedItems)) return [];
        return selectedItems.map(item => typeof item === 'object' ? item.id : item);
    };

    // Helper function to get selected objects from IDs
    const getSelectedObjects = (selectedIds, allOptions) => {
        if (!selectedIds || !Array.isArray(selectedIds)) return [];
        return selectedIds.map(id => {
            if (id === "All") return { id: "All", title: "All" };
            const found = allOptions.find(opt => getOptionValue(opt) === id);
            return found || { id, title: id }; // fallback for backward compatibility
        });
    };

    // Initialize internal selected value with IDs for internal tracking
    const [internalSelectedValue, setInternalSelectedValue] = useState(() => {
        if (selectMultiple) {
            if (Array.isArray(selectedValue)) {
                return getSelectedIds(selectedValue);
            }
            return [];
        } else {
            if (selectedValue && typeof selectedValue === 'object') {
                return selectedValue.id || selectedValue.value;
            }
            return selectedValue || (useAll ? "All" : "");
        }
    });

    // Sync internal state with external selectedValue changes
    useEffect(() => {
        if (selectMultiple) {
            if (Array.isArray(selectedValue)) {
                const ids = getSelectedIds(selectedValue);
                setInternalSelectedValue(ids);
            } else {
                setInternalSelectedValue([]);
            }
        } else {
            if (selectedValue && typeof selectedValue === 'object') {
                setInternalSelectedValue(selectedValue.id || selectedValue.value);
            } else {
                setInternalSelectedValue(selectedValue || (useAll ? "All" : ""));
            }
        }
    }, [selectedValue, selectMultiple, useAll]);

    // Function to calculate dropdown position
    const calculateDropdownPosition = () => {
        if (!ref.current || !open) return;

        // If direction is manually set, use it
        if (dropdownDirection === 'up') {
            setDropdownPosition('top');
            return;
        } else if (dropdownDirection === 'down') {
            setDropdownPosition('bottom');
            return;
        }

        // Auto calculation
        const rect = ref.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const dropdownHeight = 320; // Approximate max height (max-h-80 = 320px)

        // Check if there's enough space below
        const spaceBelow = viewportHeight - rect.bottom;
        const spaceAbove = rect.top;

        // If not enough space below but enough space above, position above
        if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
            setDropdownPosition('top');
        } else {
            setDropdownPosition('bottom');
        }
    };

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (ref.current && !ref.current.contains(event.target) && open) {
                onToggle();
                setQuery("");
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onToggle, open]);

    // Calculate position when dropdown opens
    useEffect(() => {
        if (open) {
            calculateDropdownPosition();
            // Recalculate on scroll or resize
            const handleResize = () => calculateDropdownPosition();
            window.addEventListener('resize', handleResize);
            window.addEventListener('scroll', handleResize, true);

            return () => {
                window.removeEventListener('resize', handleResize);
                window.removeEventListener('scroll', handleResize, true);
            };
        }
    }, [open]);

    const handleScroll = (e) => {
        if (!onEndScroll || isLoadingMore) return;

        const { scrollTop, scrollHeight, clientHeight } = e.target;
        if (scrollHeight - scrollTop <= clientHeight + 5) {
            setIsLoadingMore(true);
            onEndScroll()
                .then(() => {
                    setTimeout(() => {
                        setIsLoadingMore(false);
                    }, 300);
                })
                .catch(() => {
                    setIsLoadingMore(false);
                });
        }
    };

    // Helper function to get display text from option
    const getOptionDisplay = (option) => {
        if (typeof option === 'string') return option;
        return option.title || option.name || option.label || option.id;
    };

    // Helper function to get option value/id
    const getOptionValue = (option) => {
        if (typeof option === 'string') return option;
        return option.id || option.value;
    };

    const allOptions = useAll && !excludeAll ? [{ id: "All", title: "All" }, ...options] : [...options];

    const filteredOptions = allOptions.filter(option => {
        if (!onSearchChange) {
            const displayText = getOptionDisplay(option);
            return displayText.toLowerCase().includes(query.toLowerCase());
        }
        return true;
    });

    const handleOptionClick = (option) => {
        const optionValue = getOptionValue(option);

        if (selectMultiple) {
            if (optionValue === "All") {
                // Select "All" and unselect other items
                setInternalSelectedValue(["All"]);
                onChange(null); // Pass null to indicate "All" is selected
            } else {
                // Toggle individual option
                const updatedSelection = internalSelectedValue.includes(optionValue)
                    ? internalSelectedValue.filter((val) => val !== optionValue)
                    : [...internalSelectedValue.filter((val) => val !== "All"), optionValue]; // Remove "All" if selecting other items

                setInternalSelectedValue(updatedSelection);

                // Return array of selected objects instead of just IDs
                const selectedObjects = updatedSelection.map(id => {
                    if (id === "All") return { id: "All", title: "All" };
                    const found = allOptions.find(opt => getOptionValue(opt) === id);
                    return found || { id, title: id }; // fallback for backward compatibility
                });
                onChange(selectedObjects);
            }
        } else {
            const selectedId = optionValue === "All" ? null : optionValue;
            setInternalSelectedValue(selectedId);

            // Return selected object instead of just ID
            if (selectedId === null) {
                onChange(null);
            } else {
                const selectedObject = allOptions.find(opt => getOptionValue(opt) === selectedId);
                onChange(selectedObject || { id: selectedId, title: selectedId }); // fallback for backward compatibility
            }
            setQuery("");
            onToggle();
        }
    };





    return (
        <div ref={ref} className="relative">
            <button
                type="button"
                className={`${disabled ? "bg-neutral-100" : "bg-white dark:bg-base_dark"
                    } relative w-full border border-gray-300 dark:border-[#ffffff10] rounded-md shadow-sm pl-3 pr-10 py-2.5 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                aria-haspopup="listbox"
                aria-expanded={open}
                onClick={onToggle}
                disabled={disabled}
            >
                <span className="truncate flex items-center">
                    {((selectMultiple && Array.isArray(selectedValue) && selectedValue.length > 0) ||
                        (!selectMultiple && selectedValue)) && (
                            <button
                                type="button"
                                className="text-glb_red mr-2"
                                onClick={() => {
                                    // For multiple select, clear the internal selected value and trigger onChange with an empty array
                                    if (selectMultiple) {
                                        setInternalSelectedValue([]);
                                        onChange([]);
                                    } else {
                                        // For single select, clear the selected value and trigger onChange with null
                                        onChange(null);
                                        setInternalSelectedValue(null);
                                    }
                                }}
                            >
                                <Cancel01Icon size={20} />
                            </button>
                        )}
                    {loading
                        ? 'Loading...'
                        : selectMultiple
                            ? Array.isArray(selectedValue) && selectedValue.length > 0
                                ? selectedValue.length && selectedValue[0]?.id === 'All' ? 'All' : `${selectedValue.length} selected`
                                : placeholder
                            : (!useAll && internalSelectedValue === "All")
                                ? placeholder
                                : (() => {
                                    // For single selection, display the selected object's title
                                    if (selectedValue && typeof selectedValue === 'object') {
                                        return getOptionDisplay(selectedValue);
                                    } else if (internalSelectedValue) {
                                        const foundOption = allOptions.find(opt => getOptionValue(opt) === internalSelectedValue);
                                        return foundOption ? getOptionDisplay(foundOption) : internalSelectedValue;
                                    }
                                    return placeholder;
                                })()}
                </span>
                {!disabled && (
                    <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <ArrowDown01Icon />
                    </span>
                )}
            </button>

            <AnimatePresence>
                {open && (
                    <motion.ul
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.1 }}
                        className={`absolute z-[9999] w-full bg-white dark:bg-base_card shadow-lg max-h-80 rounded-md text-base ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm ${dropdownPosition === 'top'
                            ? 'bottom-full mb-1'
                            : 'top-full mt-1'
                            }`}
                        tabIndex={-1}
                        role="listbox"
                    >
                        <div className="sticky top-0 z-10 bg-white dark:bg-base_card">
                            <li className="text-gray-900 dark:text-gray-200 cursor-default select-none relative">
                                <input
                                    type="search"
                                    autoComplete="off"
                                    className="focus:ring-blue-500 py-2 px-3 focus:border-gray-500 block w-full sm:text-sm border-gray-300 dark:border-[#ffffff10] rounded-md"
                                    placeholder={'Search...'}
                                    onChange={(e) => { onSearchChange ? onSearchChange(e.target.value) : setQuery(e.target.value); }}
                                />
                            </li>
                            <hr />
                        </div>

                        <div
                            ref={scrollRef}
                            className="max-h-64 overflow-y-scroll scrollbar scrollbar-track-gray-100 scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-600 scrollbar-thumb-rounded scrollbar-thin"
                            onScroll={onEndScroll ? handleScroll : undefined}
                        >
                            {loading ? (
                                <li className="text-center py-2 text-gray-500 dark:text-gray-300">
                                    Loading...
                                </li>
                            ) : filteredOptions.length === 0 ? (
                                <li className="text-gray-900 dark:text-gray-200 cursor-default select-none relative py-2 pl-3 pr-9">
                                    No results found
                                </li>
                            ) : (
                                <>
                                    {filteredOptions.map((option, index) => {
                                        const optionValue = getOptionValue(option);
                                        const optionDisplay = getOptionDisplay(option);
                                        const isSelected = selectMultiple
                                            ? internalSelectedValue.includes(optionValue)
                                            : optionValue === internalSelectedValue || optionValue === selectedValue;

                                        return (
                                            <li
                                                key={optionValue || index}
                                                className={`text-gray-900 dark:text-gray-200 cursor-default select-none relative py-2 pl-3 pr-9 flex items-center hover:bg-gray-50 dark:hover:bg-gray-800 transition ${selectMultiple && internalSelectedValue.includes(optionValue) ? "bg-gray-200 dark:bg-gray-700" : ""}`}
                                                role="option"
                                                onClick={() => handleOptionClick(option)}
                                            >
                                                <span className="truncate">{optionDisplay}</span>
                                                {isSelected && (
                                                    <span className="text-blue-600 absolute inset-y-0 right-0 flex items-center pr-8">
                                                        <svg
                                                            className="h-5 w-5"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                        >
                                                            <path
                                                                fillRule="evenodd"
                                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                clipRule="evenodd"
                                                            />
                                                        </svg>
                                                    </span>
                                                )}
                                            </li>
                                        )
                                    })}
                                    {isLoadingMore && (
                                        <li className="text-center py-2 text-gray-500 dark:text-gray-300">
                                            ...loading
                                        </li>
                                    )}
                                </>
                            )}
                        </div>
                    </motion.ul>
                )}
            </AnimatePresence>
        </div >
    );
}

GeneralSelector.propTypes = {
    id: PropTypes.string.isRequired,
    open: PropTypes.bool.isRequired,
    disabled: PropTypes.bool,
    onToggle: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
    selectedValue: PropTypes.oneOfType([
        PropTypes.string, // For backward compatibility
        PropTypes.arrayOf(PropTypes.oneOfType([
            PropTypes.string, // For backward compatibility
            PropTypes.shape({
                id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
                title: PropTypes.string,
                name: PropTypes.string,
                label: PropTypes.string,
            })
        ])), // Array of objects for multiple selection
        PropTypes.shape({
            id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
            title: PropTypes.string,
            name: PropTypes.string,
            label: PropTypes.string,
        }) // Single object for single selection
    ]),
    options: PropTypes.arrayOf(
        PropTypes.oneOfType([
            PropTypes.string, // Still support strings for backward compatibility
            PropTypes.shape({
                id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
                title: PropTypes.string, // title, name, or label can be used for display
                name: PropTypes.string,
                label: PropTypes.string,
                value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]), // fallback for id
                sku: PropTypes.string, // for products
            })
        ])
    ).isRequired,
    loading: PropTypes.bool,
    placeholder: PropTypes.string,
    useIcon: PropTypes.bool,
    getImageSrc: PropTypes.func,
    onSearchChange: PropTypes.func,
    onEndScroll: PropTypes.func,
    useAll: PropTypes.bool,
    excludeAll: PropTypes.bool, // Add excludeAll prop type
    selectMultiple: PropTypes.bool, // Add selectMultiple prop type
    dropdownDirection: PropTypes.oneOf(['auto', 'up', 'down']), // Control dropdown direction
};
