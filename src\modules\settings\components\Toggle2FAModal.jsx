import { useState } from 'react';

import { Button } from '@heroui/button'; import { Input } from '@heroui/input';
import CustomModal from '../../shared/components/CustomModal';
import { ViewIcon, ViewOffSlashIcon } from 'hugeicons-react';
import { useDispatch, useSelector } from 'react-redux';
import { toggle2FA } from '../../../core/redux/slices/profile/profileSlice';
export default function Toggle2FAModal({ isOpen, onClose, twoFAData, setTwoFAData }) {
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const dispatch = useDispatch();
    const handle2FAToggle = async () => {        // Handle 2FA toggle logic here

        setLoading(true);
        const res = await dispatch(toggle2FA({ enable: !twoFAData?.enabled, password }));
        if (toggle2FA.fulfilled.match(res)) {
            // Update local state with the response
            setTwoFAData({
                enabled: res.payload.enabled,
                secret: res.payload.secret,
                qrCode: res.payload.qrCode,
            });
            setPassword('');
            onClose();
        }
        setLoading(false);

    };
    return (
        <CustomModal
            isOpen={isOpen}
            height="h-fit"
            onClose={onClose} width="max-w-2xl"
            showHeader={true} title={`${twoFAData?.enabled ? 'Disable' : 'Enable'} Two-Factor Authentication`}
            headerClassName="px-8 py-4 border-b" bodyClassName="p-8"
            footerContent={<div className="flex justify-center gap-4">
                <Button isLoading={loading} className={`px-8 py-2 rounded-full text-white ${twoFAData?.enabled ? 'bg-red-500' : 'bg-info'}`}
                    onClick={handle2FAToggle} isDisabled={loading || !password}
                >                        {twoFAData?.enabled ? 'Disable 2FA' : 'Enable 2FA'}
                </Button>
            </div>}        >
            <div className="space-y-6 ">
                <div className="text-center">
                    <p className="text-gray-600 dark:text-gray-400">
                        {twoFAData?.enabled
                            ? 'Enter your password to disable two-factor authentication' : 'Secure your account with Google Authenticator'}
                    </p>
                </div>
                <Input
                    type={showPassword ? 'text' : 'password'} className="my-3" placeholder="Current password"
                    value={password}
                    variant="underlined"
                    color="primary"
                    classNames={{
                        mainWrapper: 'max-w-52 mx-auto',
                        label: ["!text-[#********] dark:!text-[#FFFFFF30] "],
                    }}
                    endContent={showPassword ?
                        <ViewOffSlashIcon className="cursor-pointer" onClick={() => setShowPassword(false)} /> :
                        <ViewIcon className="cursor-pointer" onClick={() => setShowPassword(true)} />
                    }
                    autoComplete="false"
                    onChange={(e) => { setPassword(e.target.value) }} name={'oldPassword'} required />

            </div>
        </CustomModal>
    );
}















































