// src/modules/dashboard/pages/Dashboard.jsx

import React, { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@heroui/button";
import {
  Airplane01Icon,
  ArrowTurnBackwardIcon,
  PackageDeliveredIcon,
  ShippingTruck01Icon,
  ShoppingCart01Icon,
  CallEnd03Icon,
  ConnectIcon,
  CustomerService01Icon,
  MoreHorizontalCircle02Icon,
  FileLinkIcon,
  FilterIcon,
  HourglassIcon,
  Copy01Icon,
  Calendar03Icon,
  SquareArrowUp02Icon,
  TestTube01Icon,
  CheckmarkCircle01Icon,
  CircleArrowLeft02Icon,
  Dollar02Icon,
  HeadsetIcon,
  PackageProcessIcon,
} from "hugeicons-react";

import { User } from "@heroui/user";
import DashboardLayout from "../../shared/layouts/DashboardLayout";

// Components
import CallsCard from "@/modules/dashboard/components/CallsCard.jsx";
import ShippingCard from "@/modules/dashboard/components/ShippingCard.jsx";
import Gau<PERSON><PERSON><PERSON> from "@/modules/dashboard/components/GaugeChart.jsx";
import LineChartCard from "@/modules/dashboard/components/LineChartCard.jsx";
import { VerticalBarChart } from "@/modules/dashboard/components/BarChartCard.jsx";

import { useDispatch, useSelector } from "react-redux";
import { fetchCallCenterData, fetchFollwupData, fetchFundsData, fetchShippingData } from "../../../core/redux/slices/dachboard/dashboardSlice";
import { formatNumber, toSlug } from "../../../core/utils/functions";
import { setOpenFilterModal } from "../../../core/redux/slices/contentSlice";
import { motion } from "framer-motion";
import { Spinner } from "@heroui/react";
import { usePermissions } from "../../../core/providers/PermissionContext";
import { getOrderStatus, resetParams, updateParams } from "../../../core/redux/slices/orders/ordersManagementSlice";
import { useNavigate } from "react-router-dom";
import { RouteNames } from "../../../core/routes/routes";
import PriceRenderer from "../../shared/components/PriceRenderer";
import { getUserInfos } from "../../../core/services/UserHandler";

const chartData = {
  labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
  values: [1, 3, 3, 2.5, 2.5, 2, 4, 4, 3, 3, 2, 4],
};

const barChartData = [
  { label: "Sun 12", value: 1500, color: "#0258E8" },
  { label: "Mon 13", value: 2550, color: "#ED0006" },
  { label: "Tue 14", value: 2200, color: "#0258E8" },
  { label: "Wed 15", value: 2500, color: "#ED0006" },
  { label: "Thu 16", value: 2700, color: "#0258E8" },
  { label: "Fri 17", value: 2900, color: "#ED0006" },
  { label: "Sat 18", value: 2520, color: "#0258E8" },
  { label: "Sat 18", value: 2520, color: "#ED0006" },
  { label: "Sat 18", value: 2520, color: "#0258E8" },
  { label: "Sat 18", value: 2520, color: "#ED0006" },
  { label: "Sat 18", value: 2520, color: "#ED0006" },
  { label: "Sat 18", value: 2520, color: "#0258E8" },
];

// This function will be used to get the current navigation options based on permissions
const getNavOptions = (permissions) => {
  return [
    "Confirmation",
    ...(permissions?.includes("sellers.dashbord.followup") ? ["Follow Up"] : []),
    "Shipping",
  ];
};

const Navigator = ({ loading = [], navItems, currentTab, setCurrentTab }) => {
  const containerRef = useRef(); // Ref for the parent container
  const itemRefs = useRef([]); // Ref array for each nav item

  const handleTabClick = (slug, idx) => {
    setCurrentTab(slug);

    // Scroll the clicked tab into view
    const element = itemRefs.current[idx];
    if (element) {
      element.scrollIntoView({
        behavior: "smooth", // Smooth scrolling
        block: "center",
        inline: "center",
      });
    }
  };

  return (
    <div ref={containerRef} className="w-full  hide-scrollbar flex justify-start items-start gap-4">
      {navItems.map((i, idx) => {
        const slug = toSlug(i);
        return (
          <div
            ref={(el) => (itemRefs.current[idx] = el)} // Store the ref for each item
            onClick={() => handleTabClick(slug, idx)}
            key={idx}
            className="relative p-3 text-nowrap cursor-pointer hover:opacity-50 text-black dark:text-white  flex items-center justify-center gap-2"
          >
            <span
              className={`${currentTab === slug ? "opacity-100" : "opacity-30"
                } text-sm md:text-base lg:text-lg`}
            >
              {i}
            </span>
            {loading[idx] && (
              <Spinner size="sm" />
            )}
            {currentTab === slug && (
              <motion.div
                layoutId="underline" // Unique ID for shared layout animations
                className="absolute bottom-0 left-0 right-0 h-1 bg-glb_blue"
                style={{ borderRadius: "4px" }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

export default function Dashboard() {
  const dispatch = useDispatch();
  const { shipping, callCenter, followup, loadingShipping, loadingCallCenter, loadingFollowup, loadingFunds } = useSelector((state) => state.dashboard);
  const [currentTab, setCurrentTab] = useState('confirmation');

  // Use our permission hook to get permissions
  const { permissions, hasPermission,
    hasShippingService,
    hasConfirmationService, } = usePermissions();

  // Get navigation options based on permissions
  const [navOptions, setNavOptions] = useState(getNavOptions(permissions));

  // Add refs for each scrollable section
  const callCenterRef = useRef(null);
  const followUpRef = useRef(null);
  const [funds, setFunds] = useState([
    { loading: false, type: 'all', totalCount: '', totalAmount: '' },
    { loading: false, type: 'confirmed', totalCount: '', totalAmount: '' },
    { loading: false, type: 'delivered', totalCount: '', totalAmount: '' },

  ]);

  const fetchFundsOneByOne = async () => {
    const updatedFunds = [...funds]; // Clone the funds array
    for (const fund of updatedFunds) {
      try {
        fund.loading = true
        setFunds([...updatedFunds]);
        const result = await dispatch(fetchFundsData(fund.type)).unwrap();
        fund.totalCount = result.totalCount;
        fund.totalAmount = result.totalAmount;
        fund.loading = false
        setFunds([...updatedFunds]); // Update the state after each fetch
      } catch (error) {
        console.error(`Error fetching funds for type ${fund.type}:`, error);
      }
    }
  };

  // Add the state variables for horizontal scrolling
  const [isDown, setIsDown] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [activeRef, setActiveRef] = useState(null);

  // Get params from Redux state using useSelector
  const { params } = useSelector((state) => state.dashboard);
  const navigate = useNavigate();


  // Update navigation options when permissions change
  useEffect(() => {
    // Update navigation options based on current permissions
    setNavOptions(getNavOptions(permissions));

    // If current tab is follow-up but user doesn't have permission, switch to confirmation
    if (currentTab === 'follow-up' && !hasPermission('sellers.dashbord.followup')) {
      setCurrentTab('confirmation');
    }
  }, [permissions, currentTab, hasPermission]);

  // Initial data fetch for all tabs when component mounts
  // useEffect(() => {
  //   // Fetch initial data for all tabs
  //   dispatch(fetchShippingData());
  //   dispatch(fetchCallCenterData());
  //   dispatch(fetchFollwupData());
  // }, [dispatch]); // Only run on mount

  // Fetch data when params change or tab changes
  const [dataFetched, setDataFetched] = useState({
    shipping: false,
    confirmation: false,
    "follow-up": false,
  });

  useEffect(() => {
    // Fetch data based on the current tab when params change, but only if data hasn't been fetched yet
    if (currentTab === 'shipping' && !dataFetched.shipping) {
      dispatch(fetchShippingData());
      setDataFetched(prev => ({ ...prev, shipping: true })); // Mark shipping data as fetched
    } else if (currentTab === 'confirmation' && !dataFetched.confirmation) {
      dispatch(fetchCallCenterData());
      setDataFetched(prev => ({ ...prev, confirmation: true })); // Mark confirmation data as fetched
    } else if (currentTab === 'follow-up' && !dataFetched['follow-up']) {
      dispatch(fetchFollwupData());
      setDataFetched(prev => ({ ...prev, "follow-up": true })); // Mark follow-up data as fetched
    }
  }, [dispatch, currentTab, params, dataFetched]); // Fetch when tab or params change

  // We'll use the global filter modal from DashboardLayout

  // We're using the Accordion's default itemClasses

  useEffect(() => {
    fetchFundsOneByOne();
  }, []);


  const handleMouseDown = (e, ref) => {
    if (!ref.current) return;

    ref.current.style.cursor = 'grabbing';
    setActiveRef(ref);
    setIsDown(true);
    setStartX(e.pageX - ref.current.offsetLeft);
    setScrollLeft(ref.current.scrollLeft);
  };

  const handleMouseLeave = () => {
    if (!activeRef || !activeRef.current) return;

    setIsDown(false);
    activeRef.current.style.cursor = 'grab';
  };

  const handleMouseUp = () => {
    if (!activeRef || !activeRef.current) return;

    setIsDown(false);
    activeRef.current.style.cursor = 'grab';
  };

  const handleMouseMove = (e) => {
    if (!isDown || !activeRef || !activeRef.current) return;

    e.preventDefault();
    const x = e.pageX - activeRef.current.offsetLeft;
    const walk = (x - startX) * 2; // scroll-fast multiplier
    activeRef.current.scrollLeft = scrollLeft - walk;
  };


  const handleRedirection = async (status, followup = 0) => {
    await dispatch(resetParams())
    if (followup) {
      await dispatch(updateParams({ ...{ followupStatus: status, followup: 1 } }))
      navigate(RouteNames.followUp)
    } else {
      if (status === 'reallead') {
        const result = await dispatch(getOrderStatus())
        if (getOrderStatus.fulfilled.match(result)) {
          await dispatch(updateParams({ ...{ excludedStatus: result.payload.notRealStatus } }))
          navigate(RouteNames.allOrders)
        }
      } else if (status === 'shipped') {
        const result = await dispatch(getOrderStatus())
        if (getOrderStatus.fulfilled.match(result)) {
          await dispatch(updateParams({ ...{ listStatus: result.payload.shipping } }))
          navigate(RouteNames.allOrders)
        }

      } else if (status === 'upselling') {
        await dispatch(updateParams({ ...{ upsell: 'yes' } }))
        navigate(RouteNames.allOrders)
      } else {
        await dispatch(updateParams({ ...{ status: status } }))
        navigate(RouteNames.allOrders)
      }
    }
  };



  return (
    <DashboardLayout bgColor="bg-greyish">
      <div className="p-2 md:px-8 w-full flex flex-wrap justify-between items-center gap-2 mb-5">
        <User
          as="button"
          avatarProps={{
            src: "https://i.pravatar.cc/150?u=a042581f4e29026024d",
          }}
          classNames={{
            name: "text-xs text-gray-400",
            description: "font-bold text-lg text-gray-700 dark:text-gray-100",
          }}
          className="transition-transform hidden"
          name="Hello, 👋 Good Morning"
          description="Amine M'GHARI"
        />

        <h1 className="text-nowrap text-2xl font-bold flex-1">Dashboard</h1>

        {/* <div className="flex flex-row gap-2 flex-1 justify-end">
          <DateRangeDropdown
            initialStartDate={chosenRange.startDate}
            initialEndDate={chosenRange.endDate}
            onRangeChange={handleDateRangeChange}
          />

          <Dropdown>
            <DropdownTrigger>
              <Button
                variant="bordered"
                className="text-xs p-2 md:text-base md:p-4 rounded-full border-[#444444] border-1"
              >
                {selectedPeriod}
                <ArrowDown01Icon size={18} />
              </Button>
            </DropdownTrigger>
            <DropdownMenu
              variant="flat"
              aria-label="Select period"
              color="default"
              selectionMode="single"
              selectedKeys={new Set([selectedPeriod])}
              onSelectionChange={(keys) => handlePeriodChange([...keys][0])}
            >
              <DropdownItem key="Daily">Daily</DropdownItem>
              <DropdownItem key="Monthly">Monthly</DropdownItem>
              <DropdownItem key="Yearly">Yearly</DropdownItem>
            </DropdownMenu>
          </Dropdown>

        <Button
          variant="solid"
          className="text-xs p-2 md:text-base md:p-4 rounded-full bg-blue-600 text-white"
        >
          <Download02Icon size={18} />
          Export
        </Button>
      </div> */}
      </div>

      {/* Shipping  md:border-t border-t-gray-200 dark:border-t-gray-800*/}
      <div className=" w-full ">

        {hasPermission('sellers.dashbord.funds') && <div className="px-2 md:px-8">
          <div className="grid grid-cols-[33%_33%_33%] w-full gap-2">
            {funds.map((fund, index) => (
              <div key={index} className="flex justify-between items-start border transition duration-500 border-black/10 dark:border-white/10 bg-white dark:bg-transparent hover:bg-gray-50 dark:hover:bg-white/5 p-2 sm:p-4 md:p-6 rounded-2xl shadow-sm">
                {fund.loading || fund.totalAmount === '' || fund.totalCount === '' ? (
                  <div className="flex flex-col gap-2 animate-pulse">
                    <div className="h-6 w-32 bg-gray-300 dark:bg-gray-700 rounded"></div>
                    <div className="h-6 w-24 bg-gray-300 dark:bg-gray-700 rounded"></div>
                    <div className="h-6 w-16 bg-gray-300 dark:bg-gray-700 rounded"></div>
                  </div>
                ) : (
                  <div className="flex flex-col gap-2 justify-start items-start">
                    <h1 className="font-bold">{`Total ${fund.type.charAt(0).toUpperCase() + fund.type.slice(1)}`}</h1>
                    <PriceRenderer price={fund.totalAmount} additional={'USD'} />
                    <div className="flex justify-start items-start gap-2">
                      <span className="font-bold">{fund.totalCount} orders</span>
                    </div>
                  </div>
                )}
                {fund.loading || fund.totalAmount === '' || fund.totalCount === '' ?
                  <div className="flex flex-col gap-2 animate-pulse">
                    <div className="h-11 w-11 bg-gray-300 dark:bg-gray-700 rounded-full"></div>
                  </div>
                  : <div className={`flex justify-center items-center p-3 rounded-full ${index === 0 ? 'bg-glb_blue' : index === 1 ? 'bg-[#D46804]' : 'bg-glb_green'} text-white`}>
                    {index === 0 ? <Dollar02Icon size={25} /> : index === 1 ? <HeadsetIcon size={25} /> : <PackageDeliveredIcon size={25} />}
                  </div>}
              </div>
            ))}
          </div>
        </div>}
        <div className="px-2 md:px-8">
          <div className="flex flex-row justify-between items-center my-6">
            <Navigator loading={[loadingCallCenter, loadingFollowup, loadingShipping]} navItems={navOptions} currentTab={currentTab} setCurrentTab={setCurrentTab} />

          </div>

        </div>
        <div className="px-2 md:px-8 min-h-[600px]">

          {(currentTab === 'shipping') && hasShippingService() && <div >
            <div className="flex w-full flex-wrap justify-start items-stretch gap-2">
              <CallsCard
                onClick={() => handleRedirection('processing')}
                icon={<PackageProcessIcon size={18} />}
                cardBg="bg-[#8a938c]"
                loading={loadingShipping || loadingCallCenter}
                rate={shipping?.rateProcessing}
                amount={formatNumber(shipping?.totalProcessing)}
                title="Processing"
              />
              <CallsCard
                onClick={() => handleRedirection('shipped')}
                icon={<ShippingTruck01Icon size={18} />}
                cardBg="bg-[#21B539]"
                loading={loadingShipping || loadingCallCenter}
                rate={shipping?.rateShipped}
                amount={formatNumber(shipping?.totalShipped)}
                title="Shipped"
              />
              <CallsCard
                onClick={() => handleRedirection('intransit')}
                icon={<Airplane01Icon size={18} />}
                cardBg="bg-yellow-600"
                loading={loadingShipping || loadingCallCenter}
                rate={shipping?.rateInTransit}
                amount={formatNumber(shipping?.totalInTransit)}
                title="In Transit"
              />
              <CallsCard
                onClick={() => handleRedirection('delivered')}
                icon={<PackageDeliveredIcon size={18} />}
                cardBg="bg-glb_blue"
                loading={loadingShipping || loadingCallCenter}
                rate={shipping?.rateDelivered}
                amount={formatNumber(shipping?.totalDelivered)}
                title="Delivered"
              />

              <CallsCard
                onClick={() => handleRedirection('return')}
                icon={<ArrowTurnBackwardIcon size={18} />}
                cardBg="bg-glb_red"
                loading={loadingShipping || loadingCallCenter}
                rate={shipping?.rateReturn}
                amount={formatNumber(shipping?.totalReturn)}
                title="Return"
              />

            </div>
          </div>}

          {currentTab === 'confirmation' && hasConfirmationService() && <div>
            <div
              ref={callCenterRef}
              onMouseDown={(e) => handleMouseDown(e, callCenterRef)}
              onMouseLeave={handleMouseLeave}
              onMouseUp={handleMouseUp}
              onMouseMove={handleMouseMove}
              className="flex w-full flex-wrap justify-start items-stretch gap-2 ">

              <CallsCard
                onClick={() => handleRedirection('')}
                icon={<ShoppingCart01Icon size={18} />}
                cardBg="bg-[#0452D4]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateLeads}
                amount={formatNumber(callCenter?.totalLeads)}
                title="Total Leads"
              />
              <CallsCard
                onClick={() => handleRedirection('newlead')}
                icon={<ShoppingCart01Icon size={18} />}
                cardBg="bg-[#0496D4]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateNew}
                amount={formatNumber(callCenter?.totalNew)}
                title="New Leads"
              />
              <CallsCard
                onClick={() => handleRedirection('reallead')}
                icon={<FileLinkIcon size={18} />}
                cardBg="bg-[#D46804]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateReals}
                amount={formatNumber(callCenter?.totalReals)}
                title="Real Leads"
              />
              <CallsCard
                onClick={() => handleRedirection('confirmed')}
                icon={<CustomerService01Icon size={18} />}
                cardBg="bg-green-600"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateConfirmed}
                amount={formatNumber(callCenter?.totalConfirmed)}
                title="Confirmed"
              />
              <CallsCard
                onClick={() => handleRedirection('upselling')}
                icon={<SquareArrowUp02Icon size={18} />}
                cardBg="bg-[#04D419]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateUpselling}
                amount={formatNumber(callCenter?.totalUpselling)}
                title="Upselling"
              />
              <CallsCard
                onClick={() => handleRedirection('noanswer')}
                icon={<CallEnd03Icon size={18} />}
                cardBg="bg-[#D43604]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateNoAnswer}
                amount={formatNumber(callCenter?.totalNoAnswer)}
                title="No Answer"
              />
              <CallsCard
                onClick={() => handleRedirection('canceled')}
                icon={<ConnectIcon size={18} />}
                cardBg="bg-[#D40B04]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateCanceled}
                amount={formatNumber(callCenter?.totalCanceled)}
                title="Canceled"
              />

              <CallsCard
                onClick={() => handleRedirection('schedule')}
                icon={<Calendar03Icon size={18} />}
                cardBg="bg-blue-600"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateSchedule}
                amount={formatNumber(callCenter?.totalSchedule)}
                title="Schedule"
              />
              <CallsCard
                onClick={() => handleRedirection('wrongphonenumber')}
                icon={<CallEnd03Icon size={18} />}
                cardBg="bg-[#C304D4]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateWrongPhone}
                amount={formatNumber(callCenter?.totalWrongPhone)}
                title="Wrong Number"
              />
              <CallsCard
                onClick={() => handleRedirection('doubleorder')}
                icon={<Copy01Icon size={18} />}
                cardBg="bg-[#9ED404]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateDuplicate}
                amount={formatNumber(callCenter?.totalDuplicate)}
                title="Double orders"
              />

              <CallsCard
                onClick={() => handleRedirection('pending')}
                icon={<HourglassIcon size={18} />}
                cardBg="bg-orange-600"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.ratePending}
                amount={formatNumber(callCenter?.totalPending)}
                title="Pending"
              />
              <CallsCard
                onClick={() => handleRedirection('test')}
                icon={<TestTube01Icon size={18} />}
                cardBg="bg-[#04D4C3]"
                loading={loadingShipping || loadingCallCenter}
                rate={callCenter?.rateTest}
                amount={formatNumber(callCenter?.totalTest)}
                title="Test"
              />



            </div>
          </div>}
          {currentTab === 'follow-up' && hasPermission("sellers.dashbord.followup") && <div className="">
            <div
              ref={followUpRef}
              onMouseDown={(e) => handleMouseDown(e, followUpRef)}
              onMouseLeave={handleMouseLeave}
              onMouseUp={handleMouseUp}
              onMouseMove={handleMouseMove}
              className="flex w-full flex-wrap justify-start items-stretch gap-2 cursor-grab">

              <CallsCard
                onClick={() => handleRedirection('', 1)}
                icon={<ShoppingCart01Icon size={18} />}
                cardBg="bg-[#0452D4]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateTotal}
                amount={formatNumber(followup?.totalLeads)}
                title="Total Leads"
              />
              <CallsCard
                onClick={() => handleRedirection('new', 1)}
                icon={<ShoppingCart01Icon size={18} />}
                cardBg="bg-[#0496D4]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateNew}
                amount={formatNumber(followup?.totalNew)}
                title="New Leads"
              />
              <CallsCard
                onClick={() => handleRedirection('delivery', 1)}
                icon={<CustomerService01Icon size={18} />}
                cardBg="bg-[#D46804]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateDelivery}
                amount={formatNumber(followup?.totalDelivery)}
                title="Delivery Again"
              />
              <CallsCard
                onClick={() => handleRedirection('abandon', 1)}
                icon={<CallEnd03Icon size={18} />}
                cardBg="bg-[#D40B04]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateAbandon}
                amount={formatNumber(followup?.totalAbandon)}
                title="Abandon"
              />
              <CallsCard
                onClick={() => handleRedirection('no-answer', 1)}
                icon={<CallEnd03Icon size={18} />}
                cardBg="bg-[#D43604]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateNoanswer}
                amount={formatNumber(followup?.totalNoanswer)}
                title="No Answer"
              />
              <CallsCard
                icon={<CallEnd03Icon size={18} />}
                cardBg="bg-[#C304D4]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateWrongnumber}
                amount={formatNumber(followup?.totalWrongnumber)}
                title="Wrong Number"
              />
              <CallsCard
                onClick={() => handleRedirection('callback', 1)}
                icon={<CustomerService01Icon size={18} />}
                cardBg="bg-[#04D419]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateCallback}
                amount={formatNumber(followup?.totalCallback)}
                title="Call Back"
              />
              <CallsCard
                onClick={() => handleRedirection('rtc', 1)}
                icon={<CircleArrowLeft02Icon size={18} />}
                cardBg="bg-[#04D4C3]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateRtc}
                amount={formatNumber(followup?.totalRtc)}
                title="Return to Client"
              />
              <CallsCard
                onClick={() => handleRedirection('already-received', 1)}
                icon={<CheckmarkCircle01Icon size={18} />}
                cardBg="bg-[#9ED404]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rateAlreadyreceived}
                amount={formatNumber(followup?.totalAlreadyreceived)}
                title="Already Received"
              />
              <CallsCard
                onClick={() => handleRedirection('others', 1)}
                icon={<MoreHorizontalCircle02Icon size={18} />}
                cardBg="bg-[#5E4D90]"
                loading={loadingShipping || loadingCallCenter || loadingFollowup}
                rate={followup?.rate_others}
                amount={followup?.total_others}
                title="Others"
              />


            </div>
          </div>}

        </div>
        {/* Call Center Cards */}



        {/* Charts */}
        {/* <div className="flex flex-row flex-wrap items-start justify-start p-2 md:px-8">
          <div className="w-full h-full lg:w-[55%]">
            <h2 className="text-2xl font-bold my-4">Overview</h2>
            <LineChartCard
              subTitle="Product Delivery"
              title="Delivery Rate"
              data={chartData}
              percentChange={7.9}
              timeRange="+832 Increased"
            />
          </div>
          <div className="w-full h-full lg:w-[45%]">
            <h2 className="text-2xl font-bold my-4">Confirmation</h2>
            <GaugeChart
              type="semi"
              value={50}
              size={200}
              min={0}
              max={100}
              arcWidth={10}
              arcs={[{ color: "#22c55e", limit: 50 }]}
            />
          </div>
        </div>

        <div className="p-6">
          <h2 className="text-2xl font-bold my-4">Profits</h2>
          <VerticalBarChart chartData={barChartData} />
        </div> */}
      </div>

      {/* Dashboard Filter Modal is now handled by DashboardLayout */}
    </DashboardLayout >
  );
}