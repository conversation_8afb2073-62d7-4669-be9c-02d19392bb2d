import React, { useEffect, useMemo, useState } from 'react'
import CustomModal from '../../components/CustomModal';
import UnderlinedInput from '../../../settings/components/UnderlinedInput';
import GeneralSelector from '../../components/GeneralSelector';
import axios from 'axios';
import { getToken } from '../../../../core/services/TokenHandler';
import { Button } from "@heroui/button";
import { Delete02Icon, FilterIcon } from 'hugeicons-react';
import { getProductCategoriesUrl, getProductTypeUrl } from '../../../../core/redux/slices/URLs';
import { RouteNames } from '../../../../core/routes/routes';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { updateParamsSync, resetParamsSync } from '../../../../core/redux/slices/stock/products/productsSlice';
import { debounce } from 'lodash';

const ProductFilterModal = ({ isOpen, onClose }) => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { params } = useSelector((state) => state.products);

    // State for filter fields
    const [keyword, setKeyword] = useState('');
    const [sku, setSku] = useState('');
    const [archive, setArchive] = useState(0);

    // State for product types and categories
    const [loadingProductTypes, setLoadingProductTypes] = useState(false);
    const [loadingProductCategories, setLoadingProductCategories] = useState(false);
    const [productTypes, setProductTypes] = useState([]);
    const [productCategories, setProductCategories] = useState([]);

    // State for dropdowns
    const [isProductTypeOpen, setIsProductTypeOpen] = useState(false);
    const [isProductCategoryOpen, setIsProductCategoryOpen] = useState(false);
    const [isArchiveOpen, setIsArchiveOpen] = useState(false);

    // State for selected values
    const [selectedProductType, setSelectedProductType] = useState(null);
    const [selectedProductCategory, setSelectedProductCategory] = useState(null);

    // Create a debounced function to update keyword using useMemo to ensure it's stable
    const debouncedSetKeyword = useMemo(() =>
        debounce((value) => {
            setKeyword(value);
        }, 500) // 500ms delay
        , []);  // Empty dependency array ensures this is only created once

    // Cleanup debounce on component unmount to avoid memory leaks
    useEffect(() => {
        return () => {
            if (debouncedSetKeyword && debouncedSetKeyword.cancel) {
                debouncedSetKeyword.cancel();
            }
        };
    }, [debouncedSetKeyword]);

    // Fetch product types and categories sequentially
    useEffect(() => {
        const fetchData = async () => {
            // First fetch product types
            setLoadingProductTypes(true);
            try {
                console.log('Fetching product types from URL:', getProductTypeUrl);
                const response = await axios.get(`${getProductTypeUrl}`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );

                if (response.data.response !== 'success') {
                    console.error(response.data.message || 'Error fetching product types');
                } else {
                    console.log('Product Types API Response:', response.data);

                    // The API returns an object with key-value pairs, convert it to an array of objects
                    if (typeof response.data.result === 'object' && response.data.result !== null) {
                        // Convert object to array of objects with id and name properties
                        const typesArray = Object.entries(response.data.result).map(([key, value]) => ({
                            id: key,
                            name: value
                        }));
                        console.log('Converted product types to array:', typesArray);
                        setProductTypes(typesArray);
                    } else if (Array.isArray(response.data.result)) {
                        setProductTypes(response.data.result);
                    } else {
                        console.error('Product types is not a valid format:', response.data.result);
                        setProductTypes([]);
                    }
                }
            } catch (error) {
                console.error('Error fetching product types:', error.response?.data?.message || error.message);
                setProductTypes([]);
            } finally {
                setLoadingProductTypes(false);
            }

            // Then fetch product categories after product types are fetched
            setLoadingProductCategories(true);
            try {
                console.log('Fetching product categories from URL:', getProductCategoriesUrl);
                const response = await axios.get(`${getProductCategoriesUrl}`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );

                if (response.data.response !== 'success') {
                    console.error(response.data.message || 'Error fetching product categories');
                } else {
                    console.log('Product Categories API Response:', response.data);

                    // The API returns an object with key-value pairs, convert it to an array of objects
                    if (typeof response.data.result === 'object' && response.data.result !== null) {
                        // Convert object to array of objects with id and name properties
                        const categoriesArray = Object.entries(response.data.result).map(([key, value]) => ({
                            id: key,
                            name: value
                        }));
                        console.log('Converted product categories to array:', categoriesArray);
                        setProductCategories(categoriesArray);
                    } else if (Array.isArray(response.data.result)) {
                        setProductCategories(response.data.result);
                    } else {
                        console.error('Product categories is not a valid format:', response.data.result);
                        setProductCategories([]);
                    }
                }
            } catch (error) {
                console.error('Error fetching product categories:', error.response?.data?.message || error.message);
                setProductCategories([]);
            } finally {
                setLoadingProductCategories(false);
            }
        };

        fetchData();
    }, []);

    // Initialize form fields with params values when component mounts
    useEffect(() => {
        if (params.keyword) setKeyword(params.keyword);
        if (params.sku) setSku(params.sku);
        if (params.archive !== undefined) setArchive(params.archive);

        // Set selected product type if it exists in productTypes
        if (params.productType && Array.isArray(productTypes) && productTypes.length > 0) {
            try {
                const type = productTypes.find(t => t.id === params.productType);
                if (type) {
                    setSelectedProductType(type);
                }
            } catch (error) {
                console.error('Error finding product type:', error);
            }
        }

        // Set selected product category if it exists in productCategories
        if (params.category && Array.isArray(productCategories) && productCategories.length > 0) {
            try {
                const category = productCategories.find(c => c.id === params.category);
                if (category) {
                    setSelectedProductCategory(category);
                }
            } catch (error) {
                console.error('Error finding product category:', error);
            }
        }
    }, [params, productTypes, productCategories]);

    // Reset state when modal is closed
    useEffect(() => {
        if (!isOpen) {
            // Reset all state values to their initial state
            setKeyword('');
            setSku('');
            setArchive(0);
            setSelectedProductType(null);
            setSelectedProductCategory(null);
            setIsProductTypeOpen(false);
            setIsProductCategoryOpen(false);
            setIsArchiveOpen(false);

            // Cancel any pending debounced calls
            if (debouncedSetKeyword && debouncedSetKeyword.cancel) {
                debouncedSetKeyword.cancel();
            }
        }
    }, [isOpen, debouncedSetKeyword]);

    const clearFilter = () => {
        setKeyword('');
        setSku('');
        setArchive(0);
        setSelectedProductType(null);
        setSelectedProductCategory(null);
        setIsProductTypeOpen(false);
        setIsProductCategoryOpen(false);
        setIsArchiveOpen(false);

        dispatch(resetParamsSync());
        onClose();
        navigate(RouteNames.allProducts);
    };

    const handleApplyFilter = () => {
        console.log('Selected product type:', selectedProductType);
        console.log('Selected product category:', selectedProductCategory);

        const filter = {
            keyword: keyword || null,
            sku: sku || null,
            archive: archive,
            productType: selectedProductType?.id || null,
            category: selectedProductCategory?.id || null,
            page: 1, // Reset to first page when applying filters
        };

        console.log('Applying filter:', filter);
        dispatch(updateParamsSync(filter));
        onClose(); // close modal
        navigate(RouteNames.allProducts);
    };

    return (
        <CustomModal
            isOpen={isOpen}
            onClose={() => {
                onClose();
            }}
            closeOnClickOutside={false}
            title="Filter Products"
            position='top-32'
            footerContent={
                <div className="flex flex-row gap-2 flex-1 justify-center md:justify-end">
                    <div className="flex flex-row justify-end flex-1 items-center gap-4">
                        <Button
                            className="rounded-full bg-glb_blue text-white text-xs p-2 md:text-base md:p-4"
                            onClick={() => handleApplyFilter()}
                        >
                            <FilterIcon size={16} /> Apply Filter
                        </Button>
                        <Button
                            className="rounded-full bg-glb_red text-white text-xs p-2 md:text-base md:p-4"
                            onClick={() => clearFilter()}
                        >
                            <Delete02Icon size={16} /> Clear All
                        </Button>
                    </div>
                </div>
            }
        >
            <div>
                <div className="flex flex-col lg:flex-row gap-2">
                    <div className="w-full lg:w-1/2">
                        <UnderlinedInput
                            id="keyword"
                            label="Product Name"
                            value={keyword}
                            onChange={(e) => setKeyword(e.target.value)}
                            start={true}
                        />
                    </div>
                    <div className="w-full lg:w-1/2">
                        <UnderlinedInput
                            id="sku"
                            label="SKU"
                            value={sku}
                            onChange={(e) => setSku(e.target.value)}
                            start={true}
                        />
                    </div>
                </div>

                <div className="flex flex-col lg:flex-row gap-2 mt-4">
                    <div className="w-full lg:w-1/2">
                        <label htmlFor="#productType" className="block mr-2">
                            <span className="text-sm text-[#00000050] dark:text-[#FFFFFF30]">Product Type</span>
                            <GeneralSelector
                                id="productType"
                                placeholder="Select a product type"
                                useAll={false}
                                open={isProductTypeOpen}
                                onToggle={() => setIsProductTypeOpen(!isProductTypeOpen)}
                                onChange={(selectedTypeObj) => {
                                    try {
                                        setSelectedProductType(selectedTypeObj);
                                    } catch (error) {
                                        console.error('Error selecting product type:', error);
                                        setSelectedProductType(null);
                                    }
                                }}
                                selectedValue={selectedProductType}
                                options={Array.isArray(productTypes) ? productTypes : []}
                                loading={loadingProductTypes}
                            />
                        </label>
                    </div>
                    <div className="w-full lg:w-1/2">
                        <label htmlFor="#productCategory" className="block mr-2">
                            <span className="text-sm text-[#00000050] dark:text-[#FFFFFF30]">Product Category</span>
                            <GeneralSelector
                                id="productCategory"
                                placeholder="Select a product category"
                                useAll={false}
                                open={isProductCategoryOpen}
                                onToggle={() => setIsProductCategoryOpen(!isProductCategoryOpen)}
                                onChange={(selectedCategoryObj) => {
                                    try {
                                        setSelectedProductCategory(selectedCategoryObj);
                                    } catch (error) {
                                        console.error('Error selecting product category:', error);
                                        setSelectedProductCategory(null);
                                    }
                                }}
                                selectedValue={selectedProductCategory}
                                options={Array.isArray(productCategories) ? productCategories : []}
                                loading={loadingProductCategories}
                            />
                        </label>
                    </div>
                </div>

                <div className="flex flex-col lg:flex-row gap-2 mt-4">
                    <div className="w-full lg:w-1/2">
                        <label htmlFor="#archive" className="block mr-2">
                            <span className="text-sm text-[#00000050] dark:text-[#FFFFFF30]">Archive Status</span>
                            <GeneralSelector
                                id="archive"
                                placeholder="Select archive status"
                                useAll={false}
                                open={isArchiveOpen}
                                onToggle={() => setIsArchiveOpen(!isArchiveOpen)}
                                onChange={(selectedArchiveObj) => {
                                    if (selectedArchiveObj?.id === "active") {
                                        setArchive(0);
                                    } else if (selectedArchiveObj?.id === "archived") {
                                        setArchive(1);
                                    }
                                }}
                                selectedValue={archive === 0 ? { id: "active", name: "Active" } : archive === 1 ? { id: "archived", name: "Archived" } : null}
                                options={[
                                    { id: "active", name: "Active" },
                                    { id: "archived", name: "Archived" }
                                ]}
                                loading={false}
                            />
                        </label>
                    </div>
                </div>
            </div>
        </CustomModal>
    );
};

export default ProductFilterModal;
