import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { forgotPasswordOTPCheckUrl, forgotPasswordUrl, login, autologin, MainDomain, resetPasswordUrl, verify2FAUrl } from './URLs';
import { toast } from 'sonner';

// Base API URL (adjust as necessary)
const API_BASE_URL = 'https://your-api-url.com';

// Thunks for async operations
export const loginUser = createAsyncThunk(
    'auth/loginUser',
    async (credentials, { rejectWithValue }) => {
        try {
            const response = await axios.post(login, credentials);


            if (response.data.response === 'error') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error logging in' });
            }
            return response.data.result;
        } catch (error) {
            //toast.error(error.response?.data?.message || error.message);

            return rejectWithValue(error.response?.data || 'Something went wrong');
        }
    }
);
// Thunks for async operations
export const verify2F = createAsyncThunk(
    'auth/verify2fa',
    async ({ code, sellerId }, { rejectWithValue }) => {
        try {
            const response = await axios.post(verify2FAUrl, { code, sellerId });

            if (response.data.response === 'error') {
                return rejectWithValue({ status: response.data?.status, message: response.data.message || 'Error logging in' });
            }

            return response.data.result;
        } catch (error) {
            //toast.error(error.response?.data?.message || error.message);

            return rejectWithValue(error.response?.data || 'Something went wrong');
        }
    }
);
export const setAuthData = createAsyncThunk(
    'auth/setAuthData',
    async (result, { dispatch }) => {
        try {
            localStorage.setItem('xM_htkju', result.accessToken);
            localStorage.setItem('xM_htUju', JSON.stringify(result.user));

            // Dispatch a custom event to notify components about auth change
            window.dispatchEvent(new CustomEvent('auth-change'));

            return { isAuthenticated: true };
        } catch (error) {
            return rejectWithValue(error.message || 'Failed to set auth data');
        }
    }
);

export const autoLoginUser = createAsyncThunk(
    'auth/autoLoginUser',
    async (token, { rejectWithValue }) => {
        try {
            const response = await axios.post(autologin + token, {});
            const { result } = response.data;

            if (result && result.accessToken) {
                localStorage.setItem('xM_htkju', result.accessToken);
                localStorage.setItem('xM_htUju', JSON.stringify(result.user));

                // Dispatch a custom event to notify components about auth change
                window.dispatchEvent(new CustomEvent('auth-change'));

                return response.data;
            } else {
                return rejectWithValue('Invalid token or no access token received');
            }
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Auto-login failed');
        }
    }
);

export const registerUser = createAsyncThunk(
    'auth/registerUser',
    async (userData, { rejectWithValue }) => {
        try {
            const response = await axios.post(`${API_BASE_URL}/register`, userData);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Something went wrong');
        }
    }
);


export const forgotPassword = createAsyncThunk(
    'auth/forgotPassword',
    async (email, { rejectWithValue }) => {
        try {
            const response = await axios.post(forgotPasswordUrl, email);
            return response.data;

        } catch (error) {
            return rejectWithValue(error.response?.data || 'Something went wrong');
        }

    }
);

export const checkResetCode = createAsyncThunk(
    'auth/checkResetCode',
    async ({ code, email }, { rejectWithValue }) => {
        try {
            const response = await axios.post(forgotPasswordOTPCheckUrl, { code, email });
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Something went wrong');
        }
    }
);
export const resetPassword = createAsyncThunk(
    'auth/resetPassword',
    async ({ code, email, newPassword }, { rejectWithValue }) => {
        try {
            const response = await axios.post(resetPasswordUrl, { code, email, password: newPassword })
            toast.success('Password has been reset successfully');
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || 'Something went wrong');
        }
    }
);

// Async thunk for logout
export const logoutUser = createAsyncThunk(
    'auth/logoutUser',
    async (_) => {
        // Check if tokens exist in localStorage
        const accessToken = localStorage.getItem('xM_htkju');
        const userData = localStorage.getItem('xM_htUju');

        if (!accessToken || !userData) {
            // If tokens don't exist, return early
            return true;
        }

        try {
            // You can add API call to invalidate token on server if needed
            const response = await axios.post('/logout', {}, {
                headers: { Authorization: `Bearer ${accessToken}` }
            });
            if (response.data.response !== 'success') {
                console.log(response.data.message || 'Error during logout');
            }

            // Clear tokens from localStorage
            localStorage.removeItem('xM_htkju'); // Clear the access token
            localStorage.removeItem('xM_htUju'); // Clear the user data

            // Dispatch a custom event to notify components about auth change
            window.dispatchEvent(new CustomEvent('auth-change'));

            // Show success message
            //toast.success('Logged out successfully');

            return true;
        } catch (error) {
            // Even if API call fails, we still want to clear local storage
            localStorage.removeItem('xM_htkju');
            localStorage.removeItem('xM_htUju');

            // Dispatch a custom event to notify components about auth change
            window.dispatchEvent(new CustomEvent('auth-change'));

            // Show error message but still return success
            //toast.error('Error during logout, but you have been logged out locally');
            return true;
        }
    }
);

// Initial state
const initialState = {
    user: null,
    isAuthenticated: !!localStorage.getItem('xM_htkju'),
    loading: false,
    error: null,
    successMessage: null,
};

// Slice
const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        clearMessages(state) {
            state.error = null;
            state.successMessage = null;
        },
    },
    extraReducers: (builder) => {
        // Login
        builder
            .addCase(loginUser.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(loginUser.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(loginUser.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'An error occurred'; // Store only the error message
            })
            .addCase(verify2F.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(verify2F.fulfilled, (state, action) => {
                state.loading = false;
                state.error = null;
            })
            .addCase(verify2F.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'An error occurred'; // Store only the error message
            })
            // Set Auth Data
            .addCase(setAuthData.fulfilled, (state, action) => {
                state.isAuthenticated = true;
                state.error = null;
            })
            .addCase(setAuthData.rejected, (state, action) => {
                state.error = action.payload || 'Failed to set auth data';
            })
            // Auto-login
            .addCase(autoLoginUser.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(autoLoginUser.fulfilled, (state, action) => {
                state.loading = false;
                state.isAuthenticated = true;
            })
            .addCase(autoLoginUser.rejected, (state, action) => {
                state.loading = false;
            })
            .addCase(registerUser.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(registerUser.fulfilled, (state, action) => {
                state.loading = false;
                state.successMessage = action.payload.message || 'Registration successful';
            })
            .addCase(registerUser.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            }).addCase(forgotPassword.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(forgotPassword.fulfilled, (state, action) => {
                state.loading = false;
                state.successMessage = action.payload.message || 'Password reset email sent';
            })
            .addCase(forgotPassword.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'An error occurred'; // Store only the error message
            })
            .addCase(checkResetCode.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(checkResetCode.fulfilled, (state, action) => {
                state.loading = false;
                state.successMessage = action.payload.message || 'Password reset email sent';
            })
            .addCase(checkResetCode.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'An error occurred'; // Store only the error message
            })
            .addCase(resetPassword.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(resetPassword.fulfilled, (state, action) => {
                state.loading = false;
                state.successMessage = action.payload.message || 'Password reset email sent';
            })
            .addCase(resetPassword.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'An error occurred'; // Store only the error message
            })

            // Logout
            .addCase(logoutUser.pending, (state) => {
                state.loading = true;
            })
            .addCase(logoutUser.fulfilled, (state) => {
                state.loading = false;
                state.isAuthenticated = false;
                state.user = null;
            })
            .addCase(logoutUser.rejected, (state) => {
                state.loading = false;
                state.isAuthenticated = false;
                state.user = null;
            });
    },
});

// Export actions and reducer
export const { clearMessages } = authSlice.actions;
export default authSlice.reducer;
