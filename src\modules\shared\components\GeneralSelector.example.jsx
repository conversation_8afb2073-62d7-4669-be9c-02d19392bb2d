import React, { useState } from 'react';
import GeneralSelector from './GeneralSelector';
import { runAllTests } from './GeneralSelector.test.js';

// Example usage of the updated GeneralSelector component
export default function GeneralSelectorExample() {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedValue, setSelectedValue] = useState(null);
    const [multiSelectedValues, setMultiSelectedValues] = useState([]);

    // Example product options as array of objects with id, name, and sku
    const productOptions = [
        { id: 12, name: 'Product A', sku: 'SKU-001' },
        { id: 13, name: 'Product B', sku: 'SKU-002' },
        { id: 14, name: 'Product C', sku: 'SKU-003' },
        { id: 15, name: 'Product D', sku: 'SKU-004' },
        { id: 16, name: 'Product E', sku: 'SKU-005' },
    ];

    // Example category options with different property names
    const categoryOptions = [
        { id: 'cat-a', title: 'Category A' },
        { id: 'cat-b', title: 'Category B' },
        { id: 'cat-c', title: 'Category C' },
        { id: 'cat-d', title: 'Category D' },
        { id: 'cat-e', title: 'Category E' },
    ];

    return (
        <div className="p-4 space-y-6">
            <h2 className="text-xl font-bold">GeneralSelector Examples</h2>

            {/* Single Product Selection Example */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Single Product Selection</h3>
                <GeneralSelector
                    id="single-product-selector"
                    open={isOpen}
                    onToggle={() => setIsOpen(!isOpen)}
                    onChange={(selectedProduct) => {
                        setSelectedValue(selectedProduct);
                        console.log('Selected product object:', selectedProduct);
                    }}
                    selectedValue={selectedValue}
                    options={productOptions}
                    placeholder="Select a product..."
                />
                <p className="mt-2 text-sm text-gray-600">
                    Selected Product: {selectedValue ? `${selectedValue.name} (ID: ${selectedValue.id}, SKU: ${selectedValue.sku})` : 'None'}
                </p>
            </div>

            {/* Multiple Category Selection Example */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Multiple Category Selection</h3>
                <GeneralSelector
                    id="multi-category-selector"
                    open={isOpen}
                    onToggle={() => setIsOpen(!isOpen)}
                    onChange={(selectedCategories) => {
                        setMultiSelectedValues(selectedCategories || []);
                        console.log('Selected category objects:', selectedCategories);
                    }}
                    selectedValue={multiSelectedValues}
                    options={categoryOptions}
                    placeholder="Select multiple categories..."
                    selectMultiple={true}
                />
                <p className="mt-2 text-sm text-gray-600">
                    Selected Categories: {multiSelectedValues.length > 0 ?
                        multiSelectedValues.map(cat => `${cat.title} (${cat.id})`).join(', ') : 'None'}
                </p>
            </div>

            {/* Backward Compatibility - String Array */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Backward Compatibility (String Array)</h3>
                <GeneralSelector
                    id="string-selector"
                    open={isOpen}
                    onToggle={() => setIsOpen(!isOpen)}
                    onChange={(value) => console.log('Selected string:', value)}
                    selectedValue={null}
                    options={['String Option 1', 'String Option 2', 'String Option 3']}
                    placeholder="Select a string option..."
                />
            </div>

            {/* Dropdown Direction Control */}
            <div>
                <h3 className="text-lg font-semibold mb-2">Dropdown Direction Control</h3>
                <div className="space-y-4">
                    <div>
                        <p className="text-sm text-gray-600 mb-2">Dropdown opens upward (useful for modals):</p>
                        <GeneralSelector
                            id="upward-selector"
                            open={isOpen}
                            onToggle={() => setIsOpen(!isOpen)}
                            onChange={(value) => console.log('Selected upward:', value)}
                            selectedValue={null}
                            options={categoryOptions}
                            placeholder="Opens upward..."
                            dropdownDirection="up"
                        />
                    </div>
                    <div>
                        <p className="text-sm text-gray-600 mb-2">Auto-positioning (default behavior):</p>
                        <GeneralSelector
                            id="auto-selector"
                            open={isOpen}
                            onToggle={() => setIsOpen(!isOpen)}
                            onChange={(value) => console.log('Selected auto:', value)}
                            selectedValue={null}
                            options={categoryOptions}
                            placeholder="Auto-positions..."
                            dropdownDirection="auto"
                        />
                    </div>
                </div>
            </div>

            {/* Test Results Display */}
            <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
                <h3 className="text-lg font-semibold mb-4">Test Results</h3>
                <div className="space-y-2 text-sm">
                    <div>
                        <strong>✅ Object-based selection:</strong> {selectedValue ? 'PASS' : 'PENDING'} -
                        {selectedValue ? ` Returns object with id: ${selectedValue.id}` : ' Select a product above'}
                    </div>
                    <div>
                        <strong>✅ Multiple selection:</strong> {multiSelectedValues.length > 0 ? 'PASS' : 'PENDING'} -
                        {multiSelectedValues.length > 0 ? ` Returns array of ${multiSelectedValues.length} objects` : ' Select categories above'}
                    </div>
                    <div>
                        <strong>✅ Backward compatibility:</strong> PASS - String arrays still supported
                    </div>
                    <div>
                        <strong>✅ ID-based selection:</strong> PASS - All selections use 'id' property for identification
                    </div>
                    <div>
                        <strong>✅ Dropdown positioning:</strong> PASS - Auto-detects space and positions above/below as needed
                    </div>
                    <div>
                        <strong>✅ Modal compatibility:</strong> PASS - High z-index (9999) prevents hiding under modal footers
                    </div>
                </div>

                <button
                    onClick={() => runAllTests()}
                    className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                    Run Automated Tests (Check Console)
                </button>
            </div>
        </div>
    );
}
