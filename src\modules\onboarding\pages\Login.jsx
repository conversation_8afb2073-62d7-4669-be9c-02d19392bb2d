import { <PERSON><PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import BlankLayout from "@/modules/shared/layouts/BlankLayout";
import { AccountSetting02Icon, Mail01Icon, SettingsError02Icon, SmartPhone01Icon, ViewIcon, ViewOffSlashIcon } from "hugeicons-react";
import Recaptcha from "../components/Repatcha";
import ContinueWithGoogle from "@/modules/onboarding/components/ContinueWithGoogle.jsx";
import { loginSchema, registerSchema } from "@/modules/shared/schemes/userSchema.js";
import { useEffect, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { RouteNames } from "../../../core/routes/routes";
import { useDispatch, useSelector } from "react-redux";
import { loginUser, setAuthData, verify2F } from "../../../core/redux/slices/authSlice";
import InputOTP from "@/modules/shared/components/InputOTP.jsx";
import { toast } from "sonner";

export default function Login() {


    const [setCaptchaToken] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [validation, setValidation] = useState({});
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { loading, error } = useSelector((state) => state.auth);

    const [twoFacAuth, setTwoFacAuth] = useState(false)
    const [otpCode, setOtpCode] = useState('');
    const [sellerId, setSellerId] = useState(null)


    const handleEmailInputChange = async (e) => {
        const email = e.target.value;
        setEmail(email);
        const check = await registerSchema.safeParse({ email, password });
        if (!check.success) {
            setValidation(prevValidation => ({
                ...prevValidation,
                email: check.error.flatten().fieldErrors.email
            }));
        } else {
            setValidation(prevValidation => ({
                ...prevValidation,
                email: null
            }));
        }
    }
    const handlePasswordInputChange = async (e) => {
        const password = e.target.value;
        setPassword(password);
        // const check = await loginSchema.safeParse({ email, password });

        // if (!check.success) {
        //     setValidation(prevValidation => ({
        //         ...prevValidation,
        //         password: check.error.flatten().fieldErrors.password
        //     }));
        // } else {
        //     setValidation(prevValidation => ({
        //         ...prevValidation,
        //         password: null
        //     }));
        // }
    }


    const handleSubmit = async (e) => {
        e.preventDefault();

        if (twoFacAuth) {
            // Handle 2FA verification
            if (!otpCode || otpCode.length < 6) {
                toast.error("Please enter a valid 6-digit code");
                return;
            }
            if (!sellerId) {

                toast.error("Failed to verify 2FA code");
                return;
            }
            const resultAction = await dispatch(verify2F({ code: otpCode, sellerId }));
            if (verify2F.fulfilled.match(resultAction)) {
                dispatch(setAuthData(resultAction.payload));
                const from = location.state?.from?.pathname || RouteNames.dashboard;
                navigate(from, { replace: true });
            } else {
                toast.error(resultAction.payload?.message || "Failed to verify 2FA code");
            }
        } else {

            // Regular login
            const resultAction = await dispatch(loginUser({ email, password }));

            if (loginUser.fulfilled.match(resultAction)) {
                if (resultAction.payload.requires2fa) {
                    setTwoFacAuth(true);
                    setSellerId(resultAction.payload.sellerId)
                }
                else {
                    dispatch(setAuthData(resultAction.payload));
                    const from = location.state?.from?.pathname || RouteNames.dashboard;
                    navigate(from, { replace: true });
                }

            }
        }
    }

    return (
        <BlankLayout showNavbar={true}>
            <div className="px-2 md:px-6 lg:px-8 max-w-[400px] mx-auto text-center w-full">
                <h2 className="my-3 text-2xl font-bold text-primary dark:text-white">Welcome Back !</h2>
                <p className="text-gray-400">Please enter your credentials to sign-in !</p>
                {twoFacAuth ? (
                    <form action="" className="mt-8" onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            handleSubmit(e);
                        }
                    }
                    }>


                        <div className="my-6 flex flex-col py-12 px-8 rounded-lg justify-center items-center gap-2 border border-[#00000010] dark:border-[#ffffff10]">
                            <SmartPhone01Icon size={40} />
                            <span className="text-lg mb-6">Authentication code</span>
                            <InputOTP
                                length={6}
                                value={otpCode}
                                onChange={setOtpCode}
                                isInvalid={error !== null}
                                errorMessage={error}
                            />
                            {error && <div className="flex justify-start items-center text-glb_red pb-3 gap-2">
                                <SettingsError02Icon /><h6> {error}</h6></div>}
                            <div className="flex flex-col gap-4 text-sm text-gray-500">
                                <h4 className="font-bold text-base">How to enter verification code</h4>
                                <ul className="list-decimal list-inside space-y-2">
                                    <li className="text-sm">Open your Google Authenticator app</li>
                                    <li className="text-sm">Find Power Group World in the list</li>
                                    <li className="text-sm">Enter the 6-digit code shown in the app</li>
                                </ul>
                            </div>
                            <Button isLoading={loading} className="w-full my-4 font-bold text-white rounded bg-primary dark:bg-glb_blue"
                                onClick={(e) => handleSubmit(e)}
                            >Verify</Button>
                        </div>


                    </form>
                ) : (
                    <form action="" className="mt-8" onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            handleSubmit(e);
                        }
                    }
                    }>
                        {error && <div className="flex justify-start items-center text-glb_red pb-3 gap-2">
                            <SettingsError02Icon /><h6> {error}</h6></div>}
                        <Input

                            isInvalid={error !== null} type="email" placeholder="Your Email Address" value={email} classNames={{
                                inputWrapper: `bg-white dark:bg-zinc-900 border-1 ${validation?.email ? 'border-red-200 dark:border-glb_red' : 'dark:border-zinc-700 border-gray-200'} rounded-md py-6 px-4 focus:bg-normal`
                            }} endContent={<Mail01Icon />} onChange={handleEmailInputChange} required />
                        {validation?.email && (
                            <span className="text-red-400 my-2 text-sm block text-start">{validation?.email}</span>)}

                        <Input

                            isInvalid={error !== null} type={showPassword ? 'text' : 'password'} className="my-3" placeholder="Your Password"
                            value={password} classNames={{
                                inputWrapper: `bg-white  dark:bg-zinc-900 border-1 ${validation?.password ? 'border-red-200 dark:border-glb_red' : 'dark:border-zinc-700 border-gray-200'} rounded-md py-6 px-4 focus:bg-normal`
                            }} endContent={showPassword ?
                                <ViewOffSlashIcon className="cursor-pointer" onClick={() => setShowPassword(false)} /> :
                                <ViewIcon className="cursor-pointer" onClick={() => setShowPassword(true)} />
                            }
                            onChange={handlePasswordInputChange} required />
                        {validation?.password && (
                            <span className="text-red-400 my-2 text-sm block text-start">{validation?.password}</span>)}

                        <Button isLoading={loading} className="w-full my-4 font-bold text-white rounded bg-primary dark:bg-glb_blue"
                            onClick={(e) => handleSubmit(e)}
                        >Login</Button>
                        <br />
                        <div className="flex flex-row justify-between my-1">
                            <Link to={RouteNames.forgotPassword} className="text-sm">Forgot your password?</Link>
                            <Link to='https://get-started.codpowergroup.com/registration' target="_blank" className="text-sm font-bold">Signup Now</Link>
                        </div>
                        {/* <div className="flex flex-row justify-between my-1">
                        <Link to={RouteNames.forgotPassword} className="text-sm">Forgot your password?</Link>
                        <Link to={RouteNames.signup} className="text-sm font-bold">Signup Now</Link>
                    </div>
                    <br />
                    <div className="flex flex-col gap-4 py-2">
                        <Recaptcha setToken={setCaptchaToken} />
                        <span className="block text-sm">Or Continue with:</span>
                        <ContinueWithGoogle />
                    </div> */}
                    </form>)}
            </div>
        </BlankLayout>
    );
}