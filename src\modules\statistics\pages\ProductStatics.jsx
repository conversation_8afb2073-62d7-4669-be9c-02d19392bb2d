import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import DashboardLayout from '../../shared/layouts/DashboardLayout'
import { getToken } from '../../../core/services/TokenHandler';
import axios from 'axios';
import { countriesUrl, productListUrl } from '../../../core/redux/slices/URLs';
import { debounce } from 'lodash';
import GeneralSelector from '../../shared/components/GeneralSelector';
import { useDispatch, useSelector } from 'react-redux';
import { endOfMonth, endOfWeek, getLocalTimeZone, startOfMonth, startOfWeek, today } from '@internationalized/date';
import moment from 'moment';
import { I18nProvider, useLocale } from '@react-aria/i18n';
import { Button, ButtonGroup } from '@heroui/button';
import { DateRangePicker } from '@heroui/date-picker';
import { Cancel01Icon } from 'hugeicons-react';
import CountrySelector from '../../shared/components/CountrySelector';
import CustomTable from '../../shared/components/CustomTable';
import { fetchProductFunds, fetchProductStatistics, updateStatisticsParams } from '../../../core/redux/slices/statistics/productStatistics';
import { Progress } from '@heroui/progress';
import { statusColorMap } from '../../../core/utils/functions';
import { Spinner, Tooltip } from '@heroui/react';
import PriceRenderer from '../../shared/components/PriceRenderer';
import { toast } from 'sonner';

const columns = [
    { key: "product", label: "Product", isStatus: false, type: "product" },
    { key: "all", label: "Total", isStatus: true, type: "all" },
    { key: "newlead", label: "New lead", isStatus: true, type: "newlead" },
    { key: "confirmed", label: "Confirm", isStatus: true, type: "confirmed" },
    { key: "upselling", label: "Upsell", isStatus: true, type: "upselling" },
    { key: "noanswer", label: "No answer", isStatus: true, type: "noanswer" },
    { key: "canceled", label: "Cancel", isStatus: true, type: "canceled" },
    { key: "others", label: "Others", isStatus: true, type: "others" },
    { key: "intransit", label: "In transit", isStatus: true, type: "intransit" },
    { key: "shipped", label: "Shipped", isStatus: true, type: "shipped" },
    { key: "delivered", label: "Delivered", isStatus: true, type: "delivered" },
    { key: "return", label: "Return", isStatus: true, type: "return" },
    { key: "confirmedValue", label: "Confirmed Value", isStatus: true, isFund: true, type: "confirmed" },
    { key: "deliveredValue", label: "Delivered Value", isStatus: true, isFund: true, type: "delivered" },
];


function ProductStatics() {
    const [loadingProductList, setLoadingProductList] = useState(false);
    const [ProductList, setProductList] = useState([]);
    const [ProdPage, setProdPage] = useState(1);
    const [prodKey, setProdKey] = useState("");
    const [isProductListOpen, setIsProductListOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [fromCountry, setFromCountry] = useState(0);
    const [statisticsData, setStatisticsData] = useState([])
    const [loadingCountries, setLoadingCountries] = useState(false);
    const [countries, setCountries] = useState([]);
    const [filterLoading, setFilterLoading] = useState(false)
    const [isFromCountryOpen, setIsFromCountryOpen] = useState(false);
    const [loadingColumns, setLoadingColumns] = useState({}); // Track loading state for each column
    const [abortController, setAbortController] = useState(null); // For cancelling filter operations
    const [isFilterRunning, setIsFilterRunning] = useState(false); // Track if filter is currently running

    const [dateRange, setDateRange] = useState({
        start: null,
        end: null,
    });
    let { locale } = useLocale();
    // Format the date range
    const formatDate = (dateObj) => {
        if (!dateObj) return ""; // Return empty string if dateObj is null or undefined
        if (!dateObj.start || !dateObj.end)
            return `${moment().startOf("week").format("DD/MM/YYYY")} - ${moment()
                .endOf("week")
                .format("DD/MM/YYYY")}`;
        const formattedStart = dateObj?.start
            ? moment(dateObj.start.toDate()).format("DD/MM/YYYY")
            : "";
        const formattedEnd = dateObj?.end
            ? moment(dateObj.end.toDate()).format("DD/MM/YYYY")
            : "";

        return `${formattedStart} - ${formattedEnd}`;
    };
    let now = today(getLocalTimeZone());
    let lastWeek = {
        start: startOfWeek(now.subtract({ weeks: 1 }), locale),
        end: endOfWeek(now.subtract({ weeks: 1 }), locale),
    };
    let lastMonth = {
        start: startOfMonth(now.subtract({ months: 1 }), locale),
        end: endOfMonth(now.subtract({ months: 1 }), locale),
    };
    let thisWeek = {
        start: startOfWeek(now, locale),
        end: endOfWeek(now, locale),
    };
    const dispatch = useDispatch();
    const { params } = useSelector((state) => state.productStatistics);


    useEffect(() => {
        console.log(statisticsData);

    }, [statisticsData])


    useEffect(() => {
        const fetchProductList = async () => {
            // Only show loading indicator for the first page in the OrderFilterModal
            // Loading for subsequent pages is handled by GeneralSelector
            ProdPage === 1 && setLoadingProductList(true);
            try {
                if (prodKey !== "") {
                    setProdPage(1);
                }
                const response = await axios.get(
                    `${productListUrl}?page=${ProdPage}&keyword=${prodKey}`,
                    {
                        headers: {
                            Authorization: `Bearer ${getToken()}`,
                        },
                    }
                );

                if (response.data.response !== "success") {
                    console.error(
                        response.data.message || "Error fetching orders status"
                    );
                    return;
                }

                // If it's the first page or we're searching, replace the list
                // Otherwise, append the new results to the existing list
                if (ProdPage === 1 || prodKey) {
                    setProductList(response.data.result);
                } else {
                    setProductList((prevList) => [...prevList, ...response.data.result]);
                }
            } catch (error) {
                console.error(error.response?.data?.message || error.message);
            } finally {
                // Only update loading state for the first page
                ProdPage === 1 && setLoadingProductList(false);
            }
        };

        fetchProductList();
    }, [ProdPage, prodKey]);

    const renderCell = useCallback((item, columnKey, rowIndex) => {
        const data = item[columnKey] || [];
        const rate = columnKey !== 'product' ? data[0] : '';
        const count = columnKey !== 'product' ? data[1] : '';

        const loadingKey = `${rowIndex}-${columnKey}`;

        // If loading data, show Spinner
        if (loadingColumns[loadingKey]) {
            return (
                <div className="flex justify-center items-center">
                    <Spinner size="sm" />
                </div>
            );
        }

        // Check if data has been fetched for non-product columns
        const isDataFetched = columnKey === 'product' || (item[columnKey] !== undefined && item[columnKey] !== null);

        switch (columnKey) {
            case 'product':
                console.log(item[columnKey]);
                return <span>{item[columnKey].name}</span>;

            case 'confirmedValue':
                if (!isDataFetched) {
                    return (
                        <div className="flex justify-center items-center min-w-[80px]">
                            <span className="text-gray-400 text-sm italic">Unfetched</span>
                        </div>
                    );
                }
                return (
                    <div className="flex flex-col gap-1 min-w-[80px]">
                        {count !== null ? <p>{count}</p> : "--"}
                        {rate && Number(rate.replace(',', '.')) !== 0 && <PriceRenderer additionalClass="text-sm rounded-full text-black dark:text-white bg-warning/25 border-1 border-warning" price={rate} additional={'USD'} />}
                    </div>
                );

            case 'deliveredValue':
                if (!isDataFetched) {
                    return (
                        <div className="flex justify-center items-center min-w-[80px]">
                            <span className="text-gray-400 text-sm italic">Unfetched</span>
                        </div>
                    );
                }
                return (
                    <div className="flex flex-col gap-1 min-w-[80px]">
                        {count !== null ? <p>{count}</p> : "--"}
                        {rate && Number(rate.replace(',', '.')) !== 0 && <PriceRenderer additionalClass="text-sm rounded-full text-black dark:text-white bg-glb_green/25 border-1 border-glb_green" price={rate} additional={'USD'} />}
                    </div>
                );

            default:
                if (!isDataFetched) {
                    return (
                        <div className="flex justify-center items-center min-w-[80px]">
                            <span className="text-gray-400 text-sm italic">Unfetched</span>
                        </div>
                    );
                }
                return (
                    <div className="flex flex-col gap-1 min-w-[80px]">
                        <p>{count}{rate !== 0 && (!loadingColumns[loadingKey] ? <span className="text-gray-400 text-xs font-light">/{rate}%</span> : <span className="text-gray-400 text-xs font-light">--</span>)}</p>
                        {rate !== 0 && <Tooltip color='primary' content={rate !== 0 ? `${rate}%` : '--'}>
                            <Progress
                                aria-label="Loading..."
                                value={rate}
                                className="my-2"
                                classNames={{
                                    track: "bg-gray-200 dark:bg-zinc-900",
                                    indicator: statusColorMap[columnKey],
                                    base: 'h-2',
                                }}
                            />
                        </Tooltip>}
                    </div>
                );
        }
    }, [loadingColumns]);



    const Filter = async () => {
        if (!selectedProduct || selectedProduct.length === 0) {
            console.warn("No products selected for filtering.");
            return;
        }

        // Create new AbortController for this filter operation
        const controller = new AbortController();
        setAbortController(controller);
        setIsFilterRunning(true);
        setFilterLoading(true);

        const filter = {
            startDate:
                dateRange && dateRange.start
                    ? moment(dateRange.start.toDate()).format("YYYY-MM-DD")
                    : null,
            endDate:
                dateRange && dateRange.end
                    ? moment(dateRange.end.toDate()).format("YYYY-MM-DD")
                    : null,
            destinationCountry: fromCountry || null,
        }

        await dispatch(updateStatisticsParams(filter));

        try {
            // Loop through each selected product
            for (const productId of selectedProduct) {
                // Check if operation was cancelled
                if (controller.signal.aborted) {
                    // Clear any loading states
                    setLoadingColumns({});
                    return;
                }

                const productName = ProductList.find((product) => product.id === productId)?.name || "Unknown Product";

                // Check if the product's statistics already exist in statisticsData
                const existingProductStats = statisticsData.find(
                    (stats) => stats.product.id === productId
                );

                // If the statistics already exist, skip this product
                if (existingProductStats &&
                    params.startDate === filter.startDate &&
                    params.endDate === filter.endDate &&
                    params.destinationCountry === filter.destinationCountry) {
                    continue;
                }

                const productStats = { product: { name: productName, id: productId } };

                // Get the index of current product in selectedProduct array
                const productIndex = selectedProduct.indexOf(productId);

                // Loop through each column that has isStatus as true
                for (const column of columns.filter(c => c.isStatus)) {
                    // Check if operation was cancelled before each column
                    if (controller.signal.aborted) {
                        console.log("Filter operation was cancelled");
                        // Clear any loading states
                        setLoadingColumns({});
                        return;
                    }

                    const type = column.type;

                    // Set loading for this row-column pair
                    setLoadingColumns((prevState) => ({
                        ...prevState,
                        [`${productIndex}-${column.key}`]: true,
                    }));

                    try {
                        // Dispatch the action to fetch the statistics
                        const result = column.isFund ? await dispatch(
                            fetchProductFunds({ type, product: productId })
                        ).unwrap() : await dispatch(
                            fetchProductStatistics({ type, product: productId })
                        ).unwrap();
                        // Dynamically set the column name as the key
                        productStats[column.key] = column.isFund ? [result.totalAmount, result.totalCount] : [result.rate, result.count];

                        // Push the data for the current column into the statistics array
                        setStatisticsData((prevStatistics) => {
                            const updatedStatistics = [...prevStatistics];
                            const existingProductStats = updatedStatistics.find(
                                (stats) => stats.product.id === productId
                            );

                            if (existingProductStats) {
                                // Update the existing product stats
                                existingProductStats[column.key] = productStats[column.key];
                            } else {
                                // Add new product stats
                                const newProductStats = { product: { id: productId, name: productName } };
                                newProductStats[column.key] = productStats[column.key];
                                updatedStatistics.push(newProductStats);
                            }

                            return updatedStatistics;
                        });

                    } catch (error) {
                        console.error(`Failed to fetch statistics for product ${productId}, type ${type}:`, error);

                        // If the fetch fails, set the default values for that column
                        setStatisticsData((prevStatistics) => {
                            const updatedStatistics = [...prevStatistics];
                            const existingProductStats = updatedStatistics.find(
                                (stats) => stats.product.id === productId
                            );

                            if (existingProductStats) {
                                // Update the existing product stats with default values
                                existingProductStats[column.key] = [0, 0];
                            } else {
                                // Add new product stats with default values
                                const newProductStats = { product: { id: productId, name: productName } };
                                newProductStats[column.key] = [0, 0];
                                updatedStatistics.push(newProductStats);
                            }

                            return updatedStatistics;
                        });
                    } finally {
                        // Mark the column as no longer loading
                        setLoadingColumns((prevState) => ({
                            ...prevState,
                            [`${productIndex}-${column.key}`]: false,  // Set loading for this row-column pair
                        }));
                    }
                }
            }
        } catch (error) {
            console.error("Filter operation failed:", error);
            toast.error("Filter operation failed. Please try again.");
        } finally {
            setFilterLoading(false);
            setIsFilterRunning(false);
            setAbortController(null);
        }
    };

    // Function to stop the filter operation
    const stopFilter = () => {
        if (abortController) {
            abortController.abort();
            console.log("Filter operation stopped by user");
            toast.info("Filter operation stopped");
        }
    };





    // Create a debounced function to update prodKey using useMemo to ensure it's stable
    const debouncedSetProdKey = useMemo(
        () =>
            debounce((value) => {
                setProdKey(value);
            }, 500), // 500ms delay
        []
    ); // Empty dependency array ensures this is only created once

    // Cleanup debounce on component unmount to avoid memory leaks
    useEffect(() => {
        return () => {
            if (debouncedSetProdKey && debouncedSetProdKey.cancel) {
                debouncedSetProdKey.cancel();
            }
        };
    }, [debouncedSetProdKey]);

    const datePickerRef = useRef(null);
    useEffect(() => {
        const fetchCountries = async () => {
            setLoadingCountries(true);
            try {
                const response = await axios.get(`${countriesUrl}`, {
                    headers: {
                        Authorization: `Bearer ${getToken()}`,
                    },
                });

                if (response.data.response !== "success") {
                    console.error(
                        response.data.message || "Error fetching orders status"
                    );
                }
                const allOption = { id: 0, code: "all", name: "All" };
                const countriesList = [...response.data.result];
                setCountries(countriesList);
            } catch (error) {
                console.error(error.response?.data?.message || error.message);
            } finally {
                setLoadingCountries(false);
            }
        };
        fetchCountries();
    }, [])

    const handleOpenCalendar = () => {
        if (datePickerRef.current) {
            // Look for the internal selector button
            const button = datePickerRef.current.querySelector(
                'button[data-slot="selector-button"]'
            );
            if (button) button.click();
        }
    };



    return (
        <DashboardLayout
            title="Product Statistics">
            <div className="gap-1 box-border mx-auto w-full lg:w-[90%] max-w-[1200px] grid grid-cols-1 grid-rows-4 md:grid-cols-2 md:grid-rows-2 lg:grid-cols-[30%_30%_30%_10%] justify-center items-end">
                <div className="w-full ">
                    <label htmlFor="#Products" className="block mr-2">
                        <span className="text-sm text-[#00000050]  dark:text-[#FFFFFF30]">
                            Product
                        </span>
                        <GeneralSelector
                            id="Products"
                            placeholder="Select a product"
                            open={isProductListOpen}
                            onToggle={() => setIsProductListOpen(!isProductListOpen)}
                            selectMultiple={true}
                            excludeAll={true} // Exclude 'All' option
                            onChange={(val) => {

                                if (Array.isArray(val)) {
                                    // Handle multiple selections
                                    const selectedProducts = ProductList.filter((opt) => val.includes(opt.name));
                                    setSelectedProduct(selectedProducts.map((product) => product.id)); // Set IDs of selected products
                                } else {
                                    // Handle single selection
                                    const selectedProduct = ProductList.find((opt) => opt.name === val);
                                    setSelectedProduct(selectedProduct ? selectedProduct.id : null); // Set the ID if a match is found
                                }

                                if (!selectedProduct && !Array.isArray(val)) {
                                    console.warn(`No product found for the selected value: ${val}`);
                                }
                                setProdKey("");
                            }}
                            onSearchChange={(val) => {
                                // Use the debounced function instead of directly setting prodKey
                                if (debouncedSetProdKey) {
                                    debouncedSetProdKey(val);
                                } else {
                                    // Fallback in case debounce is not available
                                    setProdKey(val);
                                }
                            }}
                            onEndScroll={() => {
                                // Return a promise that loads the next page of products
                                return new Promise((resolve, reject) => {
                                    // Check if we have more products to load
                                    if (ProductList.length === 0) {
                                        reject("No more products to load");
                                        return;
                                    }

                                    // Increment the page number to trigger the useEffect
                                    setProdPage((prevPage) => prevPage + 1);

                                    // Wait for the products to load (simulate API delay)
                                    // In a real app, you might want to use a more sophisticated approach
                                    setTimeout(() => {
                                        resolve();
                                    }, 1000);
                                });
                            }}
                            defaultSelectedKeys={
                                params.productReference
                                    ? ProductList.length > 0
                                        ? ProductList.find(
                                            (p) => p.id === params.productReference
                                        )
                                        : pendingProductName
                                            ? {
                                                id: params.productReference,
                                                name: pendingProductName,
                                            }
                                            : null
                                    : null
                            }
                            selectedValue={
                                selectedProduct
                                    ? selectedProduct.name
                                    : params.productReference && pendingProductName
                                        ? pendingProductName
                                        : ""
                            }
                            options={ProductList.map((p) => p.name)}
                            loading={loadingProductList}
                        />
                    </label>
                </div>
                <div className="w-full ">
                    <div
                        ref={datePickerRef}
                        className=" cursor-pointer w-full flex justify-center items-center ">
                        <I18nProvider locale="en-GB">
                            <DateRangePicker
                                onClick={(e) => {
                                    // Prevent event from bubbling up to parent elements
                                    e.stopPropagation();
                                }}
                                calendarProps={{
                                    classNames: {
                                        base: "bg-background",
                                        headerWrapper: "pt-4 bg-background",
                                        prevButton: "border-1 border-default-200 rounded-small",
                                        nextButton: "border-1 border-default-200 rounded-small",
                                        gridHeader:
                                            "bg-background shadow-none border-b-1 border-default-100",
                                        cellButton: [
                                            "data-[today=true]:bg-default-100 data-[selected=true]:bg-transparent rounded-small",
                                            // start (pseudo)
                                            "data-[range-start=true]:before:rounded-l-small",
                                            "data-[selection-start=true]:before:rounded-l-small",
                                            // end (pseudo)
                                            "data-[range-end=true]:before:rounded-r-small",
                                            "data-[selection-end=true]:before:rounded-r-small",
                                            // start (selected)
                                            "data-[selected=true]:data-[selection-start=true]:data-[range-selection=true]:rounded-small",
                                            // end (selected)
                                            "data-[selected=true]:data-[selection-end=true]:data-[range-selection=true]:rounded-small",
                                        ],
                                    },
                                    onPress: (e) => {
                                        // Prevent calendar button clicks from bubbling up
                                        e.stopPropagation();
                                    },
                                }}
                                firstDayOfWeek="mon"
                                classNames={{
                                    label: ["!text-[#00000050] dark:!text-[#FFFFFF30] "],
                                    selectorButton: "justify-center",
                                    input: "hidden",
                                    separator: "hidden",
                                    innerWrapper: "cursor-pointer",
                                }}

                                CalendarTopContent={
                                    <ButtonGroup
                                        fullWidth
                                        className="px-3 pb-2 pt-3 bg-content1 [&>button]:text-default-500 [&>button]:border-default-200/60"
                                        radius="full"
                                        size="sm"
                                        variant="bordered">
                                        <Button
                                            onClick={() => {
                                                setDateRange(lastMonth);
                                            }}>
                                            Last Month
                                        </Button>
                                        <Button
                                            onClick={() => {
                                                setDateRange(lastWeek);
                                            }}>
                                            Last week
                                        </Button>
                                        <Button
                                            onClick={() => {
                                                setDateRange(thisWeek);
                                            }}>
                                            This Week
                                        </Button>
                                    </ButtonGroup>
                                }
                                startContent={
                                    <div
                                        className="w-full flex justify-start items-center gap-1"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleOpenCalendar();
                                        }}>
                                        {!(!dateRange.end || !dateRange.start) && (
                                            <div
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setDateRange({
                                                        start: null,
                                                        end: null,
                                                    });
                                                }}
                                                className="flex justify-center items-center p-1 bg-transparent cursor-pointer">
                                                <Cancel01Icon size={16} />
                                            </div>
                                        )}
                                        <span
                                            className={` ${!dateRange.end || !dateRange.start
                                                ? "text-sm text-[#00000050] dark:text-[#FFFFFF30]"
                                                : "text-medium text-black dark:text-white"
                                                }`}>
                                            {formatDate(dateRange)}
                                        </span>
                                    </div>
                                }
                                className="flex-grow w-full "
                                value={dateRange}
                                onChange={setDateRange}
                                color="primary"
                                label="Date"
                                variant="underlined"
                            />
                        </I18nProvider>
                    </div>
                </div>
                <div className="">
                    <label htmlFor="#fromCountry" className="block mr-2">

                        <CountrySelector
                            placeholder="Select a country"
                            open={isFromCountryOpen}
                            onToggle={() => setIsFromCountryOpen(!isFromCountryOpen)}
                            COUNTRIES={countries}
                            loading={loadingCountries}
                            id="Country"
                            useAll={true}
                            onChange={(id) => setFromCountry(id)}
                            defaultSelectedKeys={
                                params.originCountry
                                    ? countries.find(
                                        (option) => option.id === params.originCountry
                                    )
                                    : null
                            }
                            selectedValue={
                                fromCountry
                                    ? countries.find((option) => option.id === fromCountry)
                                    : null
                            }
                        />
                    </label>
                </div>
                {isFilterRunning ? (
                    <Button
                        className='bg-red-500 text-white rounded-lg animate-pulse shadow-lg shadow-red-500/50 ring-2 ring-red-400/30 hover:shadow-red-500/70 transition-all duration-300' onClick={stopFilter}
                        startContent={<Cancel01Icon size={16} />}
                    >
                        Stop
                    </Button>
                ) : (
                    <Button
                        isDisabled={filterLoading}
                        isLoading={filterLoading}
                        className='bg-glb_blue text-white rounded-lg'
                        onClick={Filter}
                    >
                        Filter
                    </Button>
                )}
            </div>
            <div className="min-w-full py-4  rounded">
                <CustomTable
                    columns={columns}
                    data={statisticsData.filter(item => selectedProduct.includes(item.product.id))}
                    // Pass filtered products based on the view

                    renderCell={renderCell}
                    className="dark:bg-gray-800 dark:text-white" // Dark mode support
                //loading={loading} // Pass loading state
                //error={error} // Pass error state


                />
            </div>

        </DashboardLayout>
    )
}

export default ProductStatics